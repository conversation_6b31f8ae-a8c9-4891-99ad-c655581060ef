{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-72:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\93a8666d95f1b2e5574541723f67c731\\transformed\\core-1.15.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "46,47,48,49,50,51,52,200", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3496,3593,3695,3797,3898,4001,4108,17803", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3588,3690,3792,3893,3996,4103,4213,17899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2936cbfd3caa73223b489c015e1e2446\\transformed\\media3-exoplayer-1.4.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,192,266,338,416,489,583,673", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "125,187,261,333,411,484,578,668,749"}, "to": {"startLines": "100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8299,8374,8436,8510,8582,8660,8733,8827,8917", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "8369,8431,8505,8577,8655,8728,8822,8912,8993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b26b2c6972d7ec14d495070bc19fa7d\\transformed\\media3-ui-1.4.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,692,780,867,945,1031,1134,1208,1276,1373,1474,1547,1615,1680,1748,1861,1972,2082,2156,2238,2312,2385,2475,2564,2632,2695,2748,2806,2854,2915,2979,3046,3110,3178,3243,3302,3367,3420,3485,3567,3649,3706", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,52,64,81,81,56,68", "endOffsets": "280,485,687,775,862,940,1026,1129,1203,1271,1368,1469,1542,1610,1675,1743,1856,1967,2077,2151,2233,2307,2380,2470,2559,2627,2690,2743,2801,2849,2910,2974,3041,3105,3173,3238,3297,3362,3415,3480,3562,3644,3701,3770"}, "to": {"startLines": "2,11,15,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,540,6296,6384,6471,6549,6635,6738,6812,6880,6977,7078,7151,7219,7284,7352,7465,7576,7686,7760,7842,7916,7989,8079,8168,8236,8998,9051,9109,9157,9218,9282,9349,9413,9481,9546,9605,9670,9723,9788,9870,9952,10009", "endLines": "10,14,18,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,52,64,81,81,56,68", "endOffsets": "330,535,737,6379,6466,6544,6630,6733,6807,6875,6972,7073,7146,7214,7279,7347,7460,7571,7681,7755,7837,7911,7984,8074,8163,8231,8294,9046,9104,9152,9213,9277,9344,9408,9476,9541,9600,9665,9718,9783,9865,9947,10004,10073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e763ae93d7b51c12c0058b9fd0f99eb\\transformed\\material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4731,4819,4906,5007,5088,5171,5270,5376,5471,5574,5660,5769,5867,5973,6094,6175,6287", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4726,4814,4901,5002,5083,5166,5265,5371,5466,5569,5655,5764,5862,5968,6089,6170,6282,6380"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10256,10377,10493,10601,10717,10812,10909,11023,11163,11286,11433,11518,11618,11716,11818,11940,12077,12182,12322,12460,12586,12782,12905,13027,13149,13275,13374,13469,13588,13725,13827,13938,14042,14187,14334,14441,14548,14632,14730,14824,14932,15020,15107,15208,15289,15372,15471,15577,15672,15775,15861,15970,16068,16174,16295,16376,16488", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "10372,10488,10596,10712,10807,10904,11018,11158,11281,11428,11513,11613,11711,11813,11935,12072,12177,12317,12455,12581,12777,12900,13022,13144,13270,13369,13464,13583,13720,13822,13933,14037,14182,14329,14436,14543,14627,14725,14819,14927,15015,15102,15203,15284,15367,15466,15572,15667,15770,15856,15965,16063,16169,16290,16371,16483,16581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\806eeec989dab5f6c89001161fc1ea32\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,850,942,1057,1141,1256,1379,1456,1531,1622,1715,1810,1904,2004,2097,2192,2287,2378,2469,2552,2662,2772,2872,2983,3092,3211,3393,17429", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "845,937,1052,1136,1251,1374,1451,1526,1617,1710,1805,1899,1999,2092,2187,2282,2373,2464,2547,2657,2767,2867,2978,3087,3206,3388,3491,17508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d8a412d674739c339798b6565d8f6383\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,1008,1096,1170,1245,1316,1386,1465,1531", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,74,70,69,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,1003,1091,1165,1240,1311,1381,1460,1526,1647"}, "to": {"startLines": "53,54,55,57,58,126,127,191,192,193,194,196,197,198,199,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4218,4313,4401,4583,4682,10078,10160,17082,17171,17258,17341,17513,17587,17662,17733,17904,17983,18049", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,74,70,69,78,65,120", "endOffsets": "4308,4396,4493,4677,4764,10155,10251,17166,17253,17336,17424,17582,17657,17728,17798,17978,18044,18165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\629a8056d9c025bf7ae40522f2b21f50\\transformed\\media3-session-1.4.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,140,249,325,423,531,618,701,792,888,971,1039,1137,1225,1320,1402,1498,1575,1667,1743,1814,1893,1974,2068", "endColumns": "84,108,75,97,107,86,82,90,95,82,67,97,87,94,81,95,76,91,75,70,78,80,93,94", "endOffsets": "135,244,320,418,526,613,696,787,883,966,1034,1132,1220,1315,1397,1493,1570,1662,1738,1809,1888,1969,2063,2158"}, "to": {"startLines": "56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4498,4769,4878,4954,5052,5160,5247,5330,5421,5517,5600,5668,5766,5854,5949,6031,6127,6204,16586,16662,16733,16812,16893,16987", "endColumns": "84,108,75,97,107,86,82,90,95,82,67,97,87,94,81,95,76,91,75,70,78,80,93,94", "endOffsets": "4578,4873,4949,5047,5155,5242,5325,5416,5512,5595,5663,5761,5849,5944,6026,6122,6199,6291,16657,16728,16807,16888,16982,17077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d9611cabb0369607a576bde775e2495\\transformed\\foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "204,205", "startColumns": "4,4", "startOffsets": "18170,18259", "endColumns": "88,96", "endOffsets": "18254,18351"}}]}]}