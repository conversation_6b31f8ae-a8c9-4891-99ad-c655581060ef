{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-72:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e0bf7504ba5a2e8560ab8846d4907f54\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "502,517,540,3299,3304", "startColumns": "4,4,4,4,4", "startOffsets": "30510,31283,32492,190813,190983", "endLines": "502,517,540,3303,3307", "endColumns": "56,64,63,24,24", "endOffsets": "30562,31343,32551,190978,191127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b26b2c6972d7ec14d495070bc19fa7d\\transformed\\media3-ui-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,39,40,41,42,43,44,45,46,47,48,49,54,61,62,63,64,65,66,67,72,73,74,75,76,77,78,79,80,81,82,83,84,85,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,235,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,287,291,295,299,303,307,311,315,316,322,333,337,341,345,349,353,357,361,365,369,373,377,390,395,400,405,418,426,436,440,444,448,451,467,493,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1143,1203,1262,1314,1364,1492,1557,1605,1654,1702,1759,1806,1861,1913,1967,2021,2075,2223,2461,2511,2560,2621,2681,2737,2797,2967,3027,3080,3137,3192,3248,3305,3354,3405,3460,3514,3573,3629,3684,3971,4036,4094,4143,4191,4242,4288,4345,4402,4464,4531,4603,4647,4704,4760,4823,4896,4966,5025,5082,5129,5184,5229,5278,5333,5387,5437,5488,5542,5601,5651,5709,5765,5818,5881,5946,6009,6061,6121,6185,6251,6309,6381,6442,6512,6582,6647,6712,6783,6878,6983,7086,7167,7250,7331,7420,7513,7606,7699,7784,7879,7972,8049,8141,8219,8299,8377,8463,8545,8638,8716,8807,8888,8977,9080,9181,9265,9361,9458,9553,9646,9738,9831,9924,10017,10100,10187,10282,10375,10477,10569,10650,10745,10838,10915,10959,11000,11045,11093,11137,11180,11229,11276,11320,11376,11429,11471,11518,11566,11626,11664,11714,11758,11797,11847,11899,11937,11984,12031,12072,12111,12149,12193,12241,12283,12321,12363,12417,12464,12501,12550,12592,12633,12674,12716,12759,12797,12833,12911,12989,13286,13556,13638,13720,13862,13940,14027,14112,14179,14242,14334,14426,14491,14554,14616,14687,14797,14908,15018,15085,15165,15236,15303,15388,15473,15536,15624,15688,15830,15930,15978,16121,16184,16246,16311,16382,16440,16498,16564,16616,16678,16754,16830,16884,16997,17276,17507,17717,17930,18140,18362,18578,18782,18820,19174,19961,20202,20442,20699,20952,21205,21440,21687,21926,22170,22391,22586,23258,23549,23845,24148,24814,25348,25822,26033,26233,26409,26517,27093,28038,29633", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,39,40,41,42,43,44,45,46,47,48,53,60,61,62,63,64,65,66,71,72,73,74,75,76,77,78,79,80,81,82,83,84,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,286,290,294,298,302,306,310,314,315,321,332,336,340,344,348,352,356,360,364,368,372,376,389,394,399,404,417,425,435,439,443,447,450,466,492,537,594", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1138,1198,1257,1309,1359,1487,1552,1600,1649,1697,1754,1801,1856,1908,1962,2016,2070,2218,2456,2506,2555,2616,2676,2732,2792,2962,3022,3075,3132,3187,3243,3300,3349,3400,3455,3509,3568,3624,3679,3966,4031,4089,4138,4186,4237,4283,4340,4397,4459,4526,4598,4642,4699,4755,4818,4891,4961,5020,5077,5124,5179,5224,5273,5328,5382,5432,5483,5537,5596,5646,5704,5760,5813,5876,5941,6004,6056,6116,6180,6246,6304,6376,6437,6507,6577,6642,6707,6778,6873,6978,7081,7162,7245,7326,7415,7508,7601,7694,7779,7874,7967,8044,8136,8214,8294,8372,8458,8540,8633,8711,8802,8883,8972,9075,9176,9260,9356,9453,9548,9641,9733,9826,9919,10012,10095,10182,10277,10370,10472,10564,10645,10740,10833,10910,10954,10995,11040,11088,11132,11175,11224,11271,11315,11371,11424,11466,11513,11561,11621,11659,11709,11753,11792,11842,11894,11932,11979,12026,12067,12106,12144,12188,12236,12278,12316,12358,12412,12459,12496,12545,12587,12628,12669,12711,12754,12792,12828,12906,12984,13281,13551,13633,13715,13857,13935,14022,14107,14174,14237,14329,14421,14486,14549,14611,14682,14792,14903,15013,15080,15160,15231,15298,15383,15468,15531,15619,15683,15825,15925,15973,16116,16179,16241,16306,16377,16435,16493,16559,16611,16673,16749,16825,16879,16992,17271,17502,17712,17925,18135,18357,18573,18777,18815,19169,19956,20197,20437,20694,20947,21200,21435,21682,21921,22165,22386,22581,23253,23544,23840,24143,24809,25343,25817,26028,26228,26404,26512,27088,28033,29628,31569"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,30,31,32,34,35,36,41,43,44,45,46,47,48,49,51,52,53,54,59,66,67,68,69,70,71,72,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,99,100,101,102,103,146,147,148,149,150,151,152,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,546,547,550,554,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,1865,1870,1874,1878,1882,1886,1890,1894,1898,1899,1905,1916,1920,1924,1928,1932,1936,1940,1944,1948,1952,1956,1960,1973,1978,1983,1988,2001,2009,2019,2023,2027,3130,3209,3347,3613,3658", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1188,1243,1303,1407,1459,1509,1686,1811,1859,1908,1956,2013,2060,2115,2223,2277,2331,2385,2533,2771,2821,2870,2931,2991,3047,3107,3277,3337,3390,3447,3502,3558,3615,3664,3715,3770,3824,3883,3939,3994,4281,4387,4445,4494,4542,4593,7445,7502,7559,7621,7688,7760,7804,17289,17345,17408,17481,17551,17610,17667,17714,17769,17814,17863,17918,17972,18022,18073,18127,18186,18236,18294,18350,18403,18466,18531,18594,18646,18706,18770,18836,18894,18966,19027,19097,19167,19232,19297,21796,21891,21996,22099,22180,22263,22344,22433,22526,22619,22712,22797,22892,22985,23062,23154,23232,23312,23390,23476,23558,23651,23729,23820,23901,23990,24093,24194,24278,24374,24471,24566,24659,24751,24844,24937,25030,25113,25200,25295,25388,25490,25582,25663,25758,25851,28592,28636,28677,28722,28770,28814,28857,28906,28953,28997,29053,29106,29148,29195,29243,29303,29341,29391,29435,29474,29524,29576,29614,29661,29708,29749,29788,29826,29870,29918,29960,29998,30040,30094,30141,30178,30227,30269,30310,30351,30393,30436,30474,32852,32930,33151,33448,38112,38194,38276,38418,38496,38583,38668,38735,38798,38890,38982,39047,39110,39172,39243,39353,39464,39574,39641,39721,39792,39859,39944,40029,40092,40180,40890,41032,41132,41180,41323,41386,41448,41513,41584,41642,41700,41766,41818,41880,41956,42032,42086,118747,119026,119257,119467,119680,119890,120112,120328,120532,120570,120924,121711,121952,122192,122449,122702,122955,123190,123437,123676,123920,124141,124336,125008,125299,125595,125898,126564,127098,127572,127783,127983,183704,187178,191981,201077,202672", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,30,31,32,34,35,39,41,43,44,45,46,47,48,49,51,52,53,58,65,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,86,87,88,89,96,97,99,100,101,102,103,146,147,148,149,150,151,152,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,546,547,553,557,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,1869,1873,1877,1881,1885,1889,1893,1897,1898,1904,1915,1919,1923,1927,1931,1935,1939,1943,1947,1951,1955,1959,1972,1977,1982,1987,2000,2008,2018,2022,2026,2030,3132,3224,3372,3657,3714", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1238,1298,1357,1454,1504,1632,1746,1854,1903,1951,2008,2055,2110,2162,2272,2326,2380,2528,2766,2816,2865,2926,2986,3042,3102,3272,3332,3385,3442,3497,3553,3610,3659,3710,3765,3819,3878,3934,3989,4276,4341,4440,4489,4537,4588,4634,7497,7554,7616,7683,7755,7799,7856,17340,17403,17476,17546,17605,17662,17709,17764,17809,17858,17913,17967,18017,18068,18122,18181,18231,18289,18345,18398,18461,18526,18589,18641,18701,18765,18831,18889,18961,19022,19092,19162,19227,19292,19363,21886,21991,22094,22175,22258,22339,22428,22521,22614,22707,22792,22887,22980,23057,23149,23227,23307,23385,23471,23553,23646,23724,23815,23896,23985,24088,24189,24273,24369,24466,24561,24654,24746,24839,24932,25025,25108,25195,25290,25383,25485,25577,25658,25753,25846,25923,28631,28672,28717,28765,28809,28852,28901,28948,28992,29048,29101,29143,29190,29238,29298,29336,29386,29430,29469,29519,29571,29609,29656,29703,29744,29783,29821,29865,29913,29955,29993,30035,30089,30136,30173,30222,30264,30305,30346,30388,30431,30469,30505,32925,33003,33443,33713,38189,38271,38413,38491,38578,38663,38730,38793,38885,38977,39042,39105,39167,39238,39348,39459,39569,39636,39716,39787,39854,39939,40024,40087,40175,40239,41027,41127,41175,41318,41381,41443,41508,41579,41637,41695,41761,41813,41875,41951,42027,42081,42194,119021,119252,119462,119675,119885,120107,120323,120527,120565,120919,121706,121947,122187,122444,122697,122950,123185,123432,123671,123915,124136,124331,125003,125294,125590,125893,126559,127093,127567,127778,127978,128154,183807,187749,192921,202667,204608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\806eeec989dab5f6c89001161fc1ea32\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "29,33,40,98,104,105,106,107,108,109,110,111,112,116,117,118,119,129,130,131,132,133,134,135,136,139,140,141,142,144,145,153,154,155,156,158,159,160,161,162,163,164,165,166,167,168,169,176,177,178,179,180,181,182,183,188,189,191,192,193,194,198,199,200,201,204,205,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,296,297,336,337,338,339,340,341,342,361,362,363,364,365,366,367,368,451,452,453,454,505,514,515,518,535,542,543,544,545,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,747,761,762,763,764,765,766,774,775,779,783,787,792,798,805,809,813,818,822,826,830,834,838,842,848,852,858,862,868,872,877,881,884,888,894,898,904,908,914,917,921,925,929,933,937,938,939,940,943,946,949,952,956,957,958,959,960,963,965,967,969,974,975,979,985,989,990,992,1004,1005,1009,1015,1019,1020,1021,1025,1052,1056,1057,1061,1089,1261,1287,1458,1484,1515,1523,1529,1545,1567,1572,1577,1587,1596,1605,1609,1616,1635,1642,1643,1652,1655,1658,1662,1666,1670,1673,1674,1679,1684,1694,1699,1706,1712,1713,1716,1720,1725,1727,1729,1732,1735,1737,1741,1744,1751,1754,1757,1761,1763,1767,1769,1771,1773,1777,1785,1793,1805,1811,1820,1823,1834,1837,1838,1843,1844,2038,2107,2177,2178,2188,2197,2198,2200,2204,2207,2210,2213,2216,2219,2222,2225,2229,2232,2235,2238,2242,2245,2249,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2275,2277,2278,2279,2280,2281,2282,2283,2284,2286,2287,2289,2290,2292,2294,2295,2297,2298,2299,2300,2301,2302,2304,2305,2306,2307,2308,2325,2327,2329,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2345,2346,2347,2348,2349,2350,2351,2353,2357,2373,2374,2375,2376,2377,2378,2382,2383,2384,2385,2387,2389,2391,2393,2395,2396,2397,2398,2400,2402,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2418,2419,2420,2421,2423,2425,2426,2428,2429,2431,2433,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2448,2449,2450,2451,2453,2454,2455,2456,2457,2459,2461,2463,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2482,2557,2560,2563,2566,2580,2593,2635,2638,2667,2694,2703,2767,3133,3143,3181,3225,3373,3397,3403,3409,3430,3554,3715,3721,3725,3752,3787,3819,3885,3905,3960,3972,3998", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1133,1362,1637,4346,4639,4694,4756,4820,4890,4951,5026,5102,5179,5464,5549,5631,5707,6230,6307,6385,6491,6597,6676,6756,6813,7002,7076,7151,7216,7324,7384,7861,7933,8006,8073,8182,8241,8300,8359,8418,8477,8531,8585,8638,8692,8746,8800,9216,9290,9369,9442,9516,9587,9659,9731,9997,10054,10158,10231,10305,10379,10596,10668,10741,10811,10972,11032,11179,11248,11317,11387,11461,11537,11601,11678,11754,11831,11896,11965,12042,12117,12186,12254,12331,12397,12458,12555,12620,12689,12788,12859,12918,12976,13033,13092,13156,13227,13299,13371,13443,13515,13582,13650,13718,13777,13840,13904,13994,14085,14145,14211,14278,14344,14414,14478,14531,14598,14659,14726,14839,14897,14960,15025,15090,15165,15238,15310,15354,15401,15447,15496,15557,15618,15679,15741,15805,15869,15933,15998,16061,16121,16182,16248,16307,16367,16429,16500,16560,17116,17202,19531,19621,19708,19796,19878,19961,20051,21343,21395,21453,21498,21564,21628,21685,21742,28150,28207,28255,28304,30684,31145,31192,31348,32253,32609,32673,32735,32795,33718,33792,33862,33940,33994,34064,34149,34197,34243,34304,34367,34433,34497,34568,34631,34696,34760,34821,34882,34934,35007,35081,35150,35225,35299,35373,35514,47601,48327,48405,48495,48583,48679,48769,49351,49440,49687,49968,50220,50505,50898,51375,51597,51819,52095,52322,52552,52782,53012,53242,53469,53888,54114,54539,54769,55197,55416,55699,55907,56038,56265,56691,56916,57343,57564,57989,58109,58385,58686,59010,59301,59615,59752,59883,59988,60230,60397,60601,60809,61080,61192,61304,61409,61526,61740,61886,62026,62112,62460,62548,62794,63212,63461,63543,63641,64298,64398,64650,65074,65329,65423,65512,65749,67773,68015,68117,68370,70526,81207,82723,93418,94946,96703,97329,97749,99010,100275,100531,100767,101314,101808,102413,102611,103191,104559,104934,105052,105590,105747,105943,106216,106472,106642,106783,106847,107212,107579,108255,108519,108857,109210,109304,109490,109796,110058,110183,110310,110549,110760,110879,111072,111249,111704,111885,112007,112266,112379,112566,112668,112775,112904,113179,113687,114183,115060,115354,115924,116073,116805,116977,117061,117397,117489,128465,133696,139067,139129,139707,140291,140382,140495,140724,140884,141036,141207,141373,141542,141709,141872,142115,142285,142458,142629,142903,143102,143307,143637,143721,143817,143913,144011,144111,144213,144315,144417,144519,144621,144721,144817,144929,145058,145181,145312,145443,145541,145655,145749,145889,146023,146119,146231,146331,146447,146543,146655,146755,146895,147031,147195,147325,147483,147633,147774,147918,148053,148165,148315,148443,148571,148707,148839,148969,149099,149211,150491,150637,150781,150919,150985,151075,151151,151255,151345,151447,151555,151663,151763,151843,151935,152033,152143,152195,152273,152379,152471,152575,152685,152807,152970,153608,153688,153788,153878,153988,154078,154319,154413,154519,154611,154711,154823,154937,155053,155169,155263,155377,155489,155591,155711,155833,155915,156019,156139,156265,156363,156457,156545,156657,156773,156895,157007,157182,157298,157384,157476,157588,157712,157779,157905,157973,158101,158245,158373,158442,158537,158652,158765,158864,158973,159084,159195,159296,159401,159501,159631,159722,159845,159939,160051,160137,160241,160337,160425,160543,160647,160751,160877,160965,161073,161173,161263,161373,161457,161559,161643,161697,161761,161867,161953,162063,162147,162406,165022,165140,165255,165335,165696,166233,167637,167715,169059,170420,170808,173651,183812,184150,185821,187754,192926,193677,193939,194139,194518,198796,204613,204842,204993,206048,207131,207981,211007,211751,213882,214222,215533", "endLines": "29,33,40,98,104,105,106,107,108,109,110,111,112,116,117,118,119,129,130,131,132,133,134,135,136,139,140,141,142,144,145,153,154,155,156,158,159,160,161,162,163,164,165,166,167,168,169,176,177,178,179,180,181,182,183,188,189,191,192,193,194,198,199,200,201,204,205,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,296,297,336,337,338,339,340,341,342,361,362,363,364,365,366,367,368,451,452,453,454,505,514,515,518,535,542,543,544,545,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,747,761,762,763,764,765,773,774,778,782,786,791,797,804,808,812,817,821,825,829,833,837,841,847,851,857,861,867,871,876,880,883,887,893,897,903,907,913,916,920,924,928,932,936,937,938,939,942,945,948,951,955,956,957,958,959,962,964,966,968,973,974,978,984,988,989,991,1003,1004,1008,1014,1018,1019,1020,1024,1051,1055,1056,1060,1088,1260,1286,1457,1483,1514,1522,1528,1544,1566,1571,1576,1586,1595,1604,1608,1615,1634,1641,1642,1651,1654,1657,1661,1665,1669,1672,1673,1678,1683,1693,1698,1705,1711,1712,1715,1719,1724,1726,1728,1731,1734,1736,1740,1743,1750,1753,1756,1760,1762,1766,1768,1770,1772,1776,1784,1792,1804,1810,1819,1822,1833,1836,1837,1842,1843,1848,2106,2176,2177,2187,2196,2197,2199,2203,2206,2209,2212,2215,2218,2221,2224,2228,2231,2234,2237,2241,2244,2248,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2274,2276,2277,2278,2279,2280,2281,2282,2283,2285,2286,2288,2289,2291,2293,2294,2296,2297,2298,2299,2300,2301,2303,2304,2305,2306,2307,2308,2326,2328,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2344,2345,2346,2347,2348,2349,2350,2352,2356,2360,2373,2374,2375,2376,2377,2381,2382,2383,2384,2386,2388,2390,2392,2394,2395,2396,2397,2399,2401,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2417,2418,2419,2420,2422,2424,2425,2427,2428,2430,2432,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2447,2448,2449,2450,2452,2453,2454,2455,2456,2458,2460,2462,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2556,2559,2562,2565,2579,2585,2602,2637,2666,2693,2702,2766,3129,3136,3170,3208,3242,3396,3402,3408,3429,3553,3573,3720,3724,3730,3786,3798,3884,3904,3959,3971,3997,4004", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1183,1402,1681,4382,4689,4751,4815,4885,4946,5021,5097,5174,5252,5544,5626,5702,5778,6302,6380,6486,6592,6671,6751,6808,6866,7071,7146,7211,7277,7379,7440,7928,8001,8068,8136,8236,8295,8354,8413,8472,8526,8580,8633,8687,8741,8795,8849,9285,9364,9437,9511,9582,9654,9726,9799,10049,10107,10226,10300,10374,10449,10663,10736,10806,10877,11027,11088,11243,11312,11382,11456,11532,11596,11673,11749,11826,11891,11960,12037,12112,12181,12249,12326,12392,12453,12550,12615,12684,12783,12854,12913,12971,13028,13087,13151,13222,13294,13366,13438,13510,13577,13645,13713,13772,13835,13899,13989,14080,14140,14206,14273,14339,14409,14473,14526,14593,14654,14721,14834,14892,14955,15020,15085,15160,15233,15305,15349,15396,15442,15491,15552,15613,15674,15736,15800,15864,15928,15993,16056,16116,16177,16243,16302,16362,16424,16495,16555,16623,17197,17284,19616,19703,19791,19873,19956,20046,20137,21390,21448,21493,21559,21623,21680,21737,21791,28202,28250,28299,28350,30713,31187,31236,31389,32280,32668,32730,32790,32847,33787,33857,33935,33989,34059,34144,34192,34238,34299,34362,34428,34492,34563,34626,34691,34755,34816,34877,34929,35002,35076,35145,35220,35294,35368,35509,35579,47649,48400,48490,48578,48674,48764,49346,49435,49682,49963,50215,50500,50893,51370,51592,51814,52090,52317,52547,52777,53007,53237,53464,53883,54109,54534,54764,55192,55411,55694,55902,56033,56260,56686,56911,57338,57559,57984,58104,58380,58681,59005,59296,59610,59747,59878,59983,60225,60392,60596,60804,61075,61187,61299,61404,61521,61735,61881,62021,62107,62455,62543,62789,63207,63456,63538,63636,64293,64393,64645,65069,65324,65418,65507,65744,67768,68010,68112,68365,70521,81202,82718,93413,94941,96698,97324,97744,99005,100270,100526,100762,101309,101803,102408,102606,103186,104554,104929,105047,105585,105742,105938,106211,106467,106637,106778,106842,107207,107574,108250,108514,108852,109205,109299,109485,109791,110053,110178,110305,110544,110755,110874,111067,111244,111699,111880,112002,112261,112374,112561,112663,112770,112899,113174,113682,114178,115055,115349,115919,116068,116800,116972,117056,117392,117484,117762,133691,139062,139124,139702,140286,140377,140490,140719,140879,141031,141202,141368,141537,141704,141867,142110,142280,142453,142624,142898,143097,143302,143632,143716,143812,143908,144006,144106,144208,144310,144412,144514,144616,144716,144812,144924,145053,145176,145307,145438,145536,145650,145744,145884,146018,146114,146226,146326,146442,146538,146650,146750,146890,147026,147190,147320,147478,147628,147769,147913,148048,148160,148310,148438,148566,148702,148834,148964,149094,149206,149346,150632,150776,150914,150980,151070,151146,151250,151340,151442,151550,151658,151758,151838,151930,152028,152138,152190,152268,152374,152466,152570,152680,152802,152965,153122,153683,153783,153873,153983,154073,154314,154408,154514,154606,154706,154818,154932,155048,155164,155258,155372,155484,155586,155706,155828,155910,156014,156134,156260,156358,156452,156540,156652,156768,156890,157002,157177,157293,157379,157471,157583,157707,157774,157900,157968,158096,158240,158368,158437,158532,158647,158760,158859,158968,159079,159190,159291,159396,159496,159626,159717,159840,159934,160046,160132,160236,160332,160420,160538,160642,160746,160872,160960,161068,161168,161258,161368,161452,161554,161638,161692,161756,161862,161948,162058,162142,162262,165017,165135,165250,165330,165691,165924,166745,167710,169054,170415,170803,173646,183699,183942,185515,187173,188321,193672,193934,194134,194513,198791,199397,204837,204988,205203,207126,207438,211002,211746,213877,214217,215528,215731"}}, {"source": "D:\\01-shuimu_01\\ShuimuVideoCourseApp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "33,23,24,25,26,27,28,29,30,7,19,20,35,36,11,12,2,3,4,13,14,17,34,5,6,18,8", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1292,853,904,955,1006,1057,1108,1159,1212,293,741,784,1385,1436,427,472,57,105,153,525,572,651,1340,201,247,696,336", "endColumns": "46,49,49,49,49,49,49,51,52,41,41,40,49,46,43,51,46,46,46,45,53,43,43,44,44,43,41", "endOffsets": "1334,898,949,1000,1051,1102,1153,1206,1260,330,778,820,1430,1478,466,519,99,147,195,566,621,690,1379,241,287,735,373"}, "to": {"startLines": "115,120,121,122,123,124,125,126,127,128,143,157,173,174,175,184,185,186,187,190,195,196,197,202,203,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5417,5783,5833,5883,5933,5983,6033,6083,6135,6188,7282,8141,9075,9125,9172,9804,9856,9903,9950,10112,10454,10508,10552,10882,10927,11093,11137", "endColumns": "46,49,49,49,49,49,49,51,52,41,41,40,49,46,43,51,46,46,46,45,53,43,43,44,44,43,41", "endOffsets": "5459,5828,5878,5928,5978,6028,6078,6130,6183,6225,7319,8177,9120,9167,9211,9851,9898,9945,9992,10153,10503,10547,10591,10922,10967,11132,11174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f96a067578d573b125b7734edf334cfa\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "536", "startColumns": "4", "startOffsets": "32285", "endColumns": "42", "endOffsets": "32323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d9611cabb0369607a576bde775e2495\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "757,758", "startColumns": "4,4", "startOffsets": "48112,48168", "endColumns": "55,54", "endOffsets": "48163,48218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57f06f2d58e8896fcc22c03ba7d554d8\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "538", "startColumns": "4", "startOffsets": "32388", "endColumns": "53", "endOffsets": "32437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e763ae93d7b51c12c0058b9fd0f99eb\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "548,675,676,677,678,679,680,681,682,683,684,687,688,689,690,691,692,693,694,695,696,697,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,1852,1862", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "33008,42424,42512,42598,42679,42763,42832,42897,42980,43086,43172,43292,43346,43415,43476,43545,43634,43729,43803,43900,43993,44091,44240,44331,44419,44515,44613,44677,44745,44832,44926,44993,45065,45137,45238,45347,45423,45492,45540,45606,45670,45744,45801,45858,45930,45980,46034,46105,46176,46246,46315,46373,46449,46520,46594,46680,46730,46800,117879,118594", "endLines": "548,675,676,677,678,679,680,681,682,683,686,687,688,689,690,691,692,693,694,695,696,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,1861,1864", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "33076,42507,42593,42674,42758,42827,42892,42975,43081,43167,43287,43341,43410,43471,43540,43629,43724,43798,43895,43988,44086,44235,44326,44414,44510,44608,44672,44740,44827,44921,44988,45060,45132,45233,45342,45418,45487,45535,45601,45665,45739,45796,45853,45925,45975,46029,46100,46171,46241,46310,46368,46444,46515,46589,46675,46725,46795,46860,118589,118742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d8a412d674739c339798b6565d8f6383\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,455,457,458,504,506,541,595,596,597,599,600,671,672,743,744,745,746,748,750,751,752,754,755,756,1849,2031,2034", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "26240,26299,26358,26418,26478,26538,26598,26658,26718,26778,26838,26898,26958,27017,27077,27137,27197,27257,27317,27377,27437,27497,27557,27617,27676,27736,27796,27855,27914,27973,28032,28091,28355,28479,28537,30633,30718,32556,36272,36337,36391,36531,36632,42235,42287,47381,47443,47497,47547,47654,47740,47786,47828,47939,47986,48022,117767,128159,128270", "endLines": "419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,455,457,458,504,506,541,595,596,597,599,600,671,672,743,744,745,746,748,750,751,752,754,755,756,1851,2033,2037", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "26294,26353,26413,26473,26533,26593,26653,26713,26773,26833,26893,26953,27012,27072,27132,27192,27252,27312,27372,27432,27492,27552,27612,27671,27731,27791,27850,27909,27968,28027,28086,28145,28424,28532,28587,30679,30768,32604,36332,36386,36452,36627,36685,42282,42342,47438,47492,47542,47596,47695,47781,47823,47863,47981,48017,48107,117874,128265,128460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2936cbfd3caa73223b489c015e1e2446\\transformed\\media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "644,645,646,647,648,649,650,651,652", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "40244,40314,40376,40441,40505,40582,40647,40737,40821", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "40309,40371,40436,40500,40577,40642,40732,40816,40885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6773774f5e74c4a64e3d2784f67c136a\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "50,333,334,335,343,344,345,508,3731", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2167,19368,19427,19475,20142,20217,20293,30827,205208", "endLines": "50,333,334,335,343,344,345,508,3751", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "2218,19422,19470,19526,20212,20288,20360,30888,206043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b499c1337a264aa344a20a2174939e0c\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "503", "startColumns": "4", "startOffsets": "30567", "endColumns": "65", "endOffsets": "30628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa93d5acfdc661a1edf197bac1dcd45\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "539", "startColumns": "4", "startOffsets": "32442", "endColumns": "49", "endOffsets": "32487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1d6314c12ce0c28d76a26cc8c2e03ba2\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2603,2619,2625,3799,3815", "startColumns": "4,4,4,4,4", "startOffsets": "166750,167175,167353,207443,207854", "endLines": "2618,2624,2634,3814,3818", "endColumns": "24,24,24,24,24", "endOffsets": "167170,167348,167632,207849,207976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a15c2856ac6517c35961a92fd0e80d18\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "456", "startColumns": "4", "startOffsets": "28429", "endColumns": "49", "endOffsets": "28474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\989db46b280c4d5d2499ac718f2ee7b8\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "507,513", "startColumns": "4,4", "startOffsets": "30773,31078", "endColumns": "53,66", "endOffsets": "30822,31140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79940b36c0fc4498c4e3ccf1c93d915e\\transformed\\navigation-runtime-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "512,2586,3602,3605", "startColumns": "4,4,4,4", "startOffsets": "31025,165929,200665,200780", "endLines": "512,2592,3604,3607", "endColumns": "52,24,24,24", "endOffsets": "31073,166228,200775,200890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\93a8666d95f1b2e5574541723f67c731\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "42,113,114,137,138,170,171,289,290,291,292,293,294,295,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,416,417,418,509,510,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,549,588,589,590,591,592,593,594,753,2309,2310,2315,2318,2323,2480,2481,3137,3171,3243,3278,3308,3341", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1751,5257,5329,6871,6936,8854,8923,16628,16698,16766,16838,16908,16969,17043,20365,20426,20487,20549,20613,20675,20736,20804,20904,20964,21030,21103,21172,21229,21281,26027,26099,26175,30893,30928,31394,31449,31512,31567,31625,31683,31744,31807,31864,31915,31965,32026,32083,32149,32183,32218,33081,35761,35828,35900,35969,36038,36112,36184,47868,149351,149468,149735,150028,150295,162267,162339,183947,185520,188326,190132,191132,191814", "endLines": "42,113,114,137,138,170,171,289,290,291,292,293,294,295,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,416,417,418,509,510,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,549,588,589,590,591,592,593,594,753,2309,2313,2315,2321,2323,2480,2481,3142,3180,3277,3298,3340,3346", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1806,5324,5412,6931,6997,8918,8981,16693,16761,16833,16903,16964,17038,17111,20421,20482,20544,20608,20670,20731,20799,20899,20959,21025,21098,21167,21224,21276,21338,26094,26170,26235,30923,30958,31444,31507,31562,31620,31678,31739,31802,31859,31910,31960,32021,32078,32144,32178,32213,32248,33146,35823,35895,35964,36033,36107,36179,36267,47934,149463,149664,149840,150224,150419,162334,162401,184145,185816,190127,190808,191809,191976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ffbc5c7141168efc0a42bc6d31641018\\transformed\\activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "516,537", "startColumns": "4,4", "startOffsets": "31241,32328", "endColumns": "41,59", "endOffsets": "31278,32383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5927188613bf648d36c0e8e1ace96d36\\transformed\\navigation-common-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3574,3587,3593,3599,3608", "startColumns": "4,4,4,4,4", "startOffsets": "199402,200041,200285,200532,200895", "endLines": "3586,3592,3598,3601,3612", "endColumns": "24,24,24,24,24", "endOffsets": "200036,200280,200527,200660,201072"}}, {"source": "D:\\01-shuimu_01\\ShuimuVideoCourseApp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "142", "endLines": "14", "endColumns": "12", "endOffsets": "720"}, "to": {"startLines": "2361", "startColumns": "4", "startOffsets": "153127", "endLines": "2372", "endColumns": "12", "endOffsets": "153603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a7fc9c4b071d7dc6aceaed142f6d075b\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "585", "startColumns": "4", "startOffsets": "35584", "endColumns": "82", "endOffsets": "35662"}}, {"source": "D:\\01-shuimu_01\\ShuimuVideoCourseApp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,9,6,4,5,7,10,8,3", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "57,367,240,161,199,277,418,320,102", "endColumns": "43,49,35,36,39,41,39,45,57", "endOffsets": "96,412,271,193,234,314,453,361,155"}, "to": {"startLines": "586,587,670,673,674,742,749,759,760", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "35667,35711,42199,42347,42384,47339,47700,48223,48269", "endColumns": "43,49,35,36,39,41,39,45,57", "endOffsets": "35706,35756,42230,42379,42419,47376,47735,48264,48322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\629a8056d9c025bf7ae40522f2b21f50\\transformed\\media3-session-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,5,6,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,387,509,583,672,745,841,936,1019,1103,1184,1269,1349,1413,1507,1586,1680,1754,1845,1917,2005,2073,2139,2215,2297,2384,2479,2545,2667,2728,2794", "endColumns": "88,98,61,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "139,382,444,578,667,740,836,931,1014,1098,1179,1264,1344,1408,1502,1581,1675,1749,1840,1912,2000,2068,2134,2210,2292,2379,2474,2540,2662,2723,2789,2856"}, "to": {"startLines": "172,415,511,598,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,736,737,738,739,740,741,2314,2316,2317,2322,2324", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8986,25928,30963,36457,36690,36779,36852,36948,37043,37126,37210,37291,37376,37456,37520,37614,37693,37787,37861,37952,38024,46865,46933,46999,47075,47157,47244,149669,149845,149967,150229,150424", "endColumns": "88,98,61,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "9070,26022,31020,36526,36774,36847,36943,37038,37121,37205,37286,37371,37451,37515,37609,37688,37782,37856,37947,38019,38107,46928,46994,47070,47152,47239,47334,149730,149962,150023,150290,150486"}}]}]}