{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-72:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\93a8666d95f1b2e5574541723f67c731\\transformed\\core-1.15.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "50,51,52,53,54,55,56,204", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3737,3835,3937,4037,4138,4244,4347,18108", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3830,3932,4032,4133,4239,4342,4463,18204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d8a412d674739c339798b6565d8f6383\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,987,1074,1146,1230,1308,1384,1469,1539", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,982,1069,1141,1225,1303,1379,1464,1534,1657"}, "to": {"startLines": "57,58,59,61,62,130,131,195,196,197,198,200,201,202,203,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4468,4561,4645,4813,4916,10312,10392,17376,17464,17546,17629,17798,17870,17954,18032,18209,18294,18364", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "4556,4640,4734,4911,4997,10387,10476,17459,17541,17624,17711,17865,17949,18027,18103,18289,18359,18482"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\629a8056d9c025bf7ae40522f2b21f50\\transformed\\media3-session-1.4.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,238,318,446,542,639,711,804,909,996,1066,1178,1271,1369,1449,1554,1635,1726,1798,1869,1950,2035,2136", "endColumns": "73,108,79,127,95,96,71,92,104,86,69,111,92,97,79,104,80,90,71,70,80,84,100,105", "endOffsets": "124,233,313,441,537,634,706,799,904,991,1061,1173,1266,1364,1444,1549,1630,1721,1793,1864,1945,2030,2131,2237"}, "to": {"startLines": "60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,189,190,191,192,193,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4739,5002,5111,5191,5319,5415,5512,5584,5677,5782,5869,5939,6051,6144,6242,6322,6427,6508,16860,16932,17003,17084,17169,17270", "endColumns": "73,108,79,127,95,96,71,92,104,86,69,111,92,97,79,104,80,90,71,70,80,84,100,105", "endOffsets": "4808,5106,5186,5314,5410,5507,5579,5672,5777,5864,5934,6046,6139,6237,6317,6422,6503,6594,16927,16998,17079,17164,17265,17371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d9611cabb0369607a576bde775e2495\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,102", "endOffsets": "137,240"}, "to": {"startLines": "208,209", "startColumns": "4,4", "startOffsets": "18487,18574", "endColumns": "86,102", "endOffsets": "18569,18672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e763ae93d7b51c12c0058b9fd0f99eb\\transformed\\material3-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,621,718,832,955,1070,1215,1299,1410,1503,1600,1714,1837,1953,2100,2246,2384,2561,2693,2818,2947,3069,3163,3261,3387,3520,3619,3730,3839,3989,4142,4250,4350,4435,4530,4626,4744,4830,4917,5017,5104,5191,5291,5397,5493,5591,5680,5788,5884,5984,6130,6220,6338", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "167,282,401,518,616,713,827,950,1065,1210,1294,1405,1498,1595,1709,1832,1948,2095,2241,2379,2556,2688,2813,2942,3064,3158,3256,3382,3515,3614,3725,3834,3984,4137,4245,4345,4430,4525,4621,4739,4825,4912,5012,5099,5186,5286,5392,5488,5586,5675,5783,5879,5979,6125,6215,6333,6429"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10481,10598,10713,10832,10949,11047,11144,11258,11381,11496,11641,11725,11836,11929,12026,12140,12263,12379,12526,12672,12810,12987,13119,13244,13373,13495,13589,13687,13813,13946,14045,14156,14265,14415,14568,14676,14776,14861,14956,15052,15170,15256,15343,15443,15530,15617,15717,15823,15919,16017,16106,16214,16310,16410,16556,16646,16764", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "10593,10708,10827,10944,11042,11139,11253,11376,11491,11636,11720,11831,11924,12021,12135,12258,12374,12521,12667,12805,12982,13114,13239,13368,13490,13584,13682,13808,13941,14040,14151,14260,14410,14563,14671,14771,14856,14951,15047,15165,15251,15338,15438,15525,15612,15712,15818,15914,16012,16101,16209,16305,16405,16551,16641,16759,16855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\806eeec989dab5f6c89001161fc1ea32\\transformed\\appcompat-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1007,1127,1230,1346,1432,1537,1656,1736,1813,1905,1999,2094,2188,2283,2377,2473,2568,2660,2752,2833,2939,3044,3142,3250,3356,3464,3637,17716", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "1122,1225,1341,1427,1532,1651,1731,1808,1900,1994,2089,2183,2278,2372,2468,2563,2655,2747,2828,2934,3039,3137,3245,3351,3459,3632,3732,17793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2936cbfd3caa73223b489c015e1e2446\\transformed\\media3-exoplayer-1.4.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,257,320,397,465,564,660", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "121,185,252,315,392,460,559,655,725"}, "to": {"startLines": "104,105,106,107,108,109,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8568,8639,8703,8770,8833,8910,8978,9077,9173", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "8634,8698,8765,8828,8905,8973,9072,9168,9238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b26b2c6972d7ec14d495070bc19fa7d\\transformed\\media3-ui-1.4.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,627,957,1040,1123,1206,1304,1402,1491,1555,1648,1742,1807,1872,1937,2005,2100,2194,2294,2371,2450,2519,2609,2702,2795,2861,2926,2979,3039,3087,3148,3221,3289,3354,3427,3492,3550,3616,3668,3727,3800,3873,3928", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,94,93,99,76,78,68,89,92,92,65,64,52,59,47,60,72,67,64,72,64,57,65,51,58,72,72,54,66", "endOffsets": "283,622,952,1035,1118,1201,1299,1397,1486,1550,1643,1737,1802,1867,1932,2000,2095,2189,2289,2366,2445,2514,2604,2697,2790,2856,2921,2974,3034,3082,3143,3216,3284,3349,3422,3487,3545,3611,3663,3722,3795,3868,3923,3990"}, "to": {"startLines": "2,11,17,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,677,6599,6682,6765,6848,6946,7044,7133,7197,7290,7384,7449,7514,7579,7647,7742,7836,7936,8013,8092,8161,8251,8344,8437,8503,9243,9296,9356,9404,9465,9538,9606,9671,9744,9809,9867,9933,9985,10044,10117,10190,10245", "endLines": "10,16,22,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,94,93,99,76,78,68,89,92,92,65,64,52,59,47,60,72,67,64,72,64,57,65,51,58,72,72,54,66", "endOffsets": "333,672,1002,6677,6760,6843,6941,7039,7128,7192,7285,7379,7444,7509,7574,7642,7737,7831,7931,8008,8087,8156,8246,8339,8432,8498,8563,9291,9351,9399,9460,9533,9601,9666,9739,9804,9862,9928,9980,10039,10112,10185,10240,10307"}}]}]}