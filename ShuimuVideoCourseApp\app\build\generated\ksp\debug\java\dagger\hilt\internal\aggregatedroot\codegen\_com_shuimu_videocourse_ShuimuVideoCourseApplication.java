package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.shuimu.videocourse.ShuimuVideoCourseApplication",
    rootPackage = "com.shuimu.videocourse",
    originatingRoot = "com.shuimu.videocourse.ShuimuVideoCourseApplication",
    originatingRootPackage = "com.shuimu.videocourse",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "ShuimuVideoCourseApplication",
    originatingRootSimpleNames = "ShuimuVideoCourseApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_shuimu_videocourse_ShuimuVideoCourseApplication {
}
