package com.shuimu.videocourse.presentation.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.videocourse.presentation.components.components_full.navigation.TopAppBar
import com.shuimu.videocourse.presentation.components.components_full.display.CategoryCard
import com.shuimu.videocourse.presentation.components.components_full.display.SeriesHeader
import com.shuimu.videocourse.presentation.components.components_full.status.LoadingIndicator
import com.shuimu.videocourse.presentation.components.components_full.status.ErrorMessage
import com.shuimu.videocourse.presentation.viewmodel.HomeViewModel
import com.shuimu.videocourse.presentation.theme.ShuimuVideoCourseTheme

/**
 * 首页 - 对应01-首页.html
 * 
 * 功能特性：
 * - 3层结构展示（系列-分类-视频）
 * - 免费体验系列置顶
 * - 动态价格计算
 * - 搜索功能
 * - 购买状态显示
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onNavigateToVideoPlayer: (String) -> Unit = {},
    onNavigateToPayment: (String) -> Unit = {},
    onNavigateToLogin: () -> Unit = {},
    viewModel: HomeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
    ) {
        // 顶部导航栏
        TopAppBar(
            title = "水幕视频课程",
            onSearchClick = { viewModel.toggleSearch() },
            onUserClick = { 
                if (uiState.isLoggedIn) {
                    // 已登录，显示用户信息
                } else {
                    onNavigateToLogin()
                }
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 主要内容区域
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    LoadingIndicator(message = "加载课程中...")
                }
            }
            
            uiState.error != null -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    ErrorMessage(
                        message = uiState.error!!,
                        onRetry = { viewModel.loadCourses() }
                    )
                }
            }
            
            else -> {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 免费体验系列（置顶）
                    uiState.freeSeries?.let { series ->
                        item {
                            SeriesHeader(
                                series = series,
                                isFree = true,
                                onToggleExpanded = { viewModel.toggleSeriesExpanded(series.id) },
                                onPurchase = { /* 免费系列不需要购买 */ }
                            )
                        }
                    }
                    
                    // 全套选项（动态价格）
                    if (uiState.bundlePrice > 0) {
                        item {
                            SeriesHeader(
                                title = "全套课程",
                                price = uiState.bundlePrice,
                                isBundle = true,
                                onPurchase = { onNavigateToPayment("bundle") }
                            )
                        }
                    }
                    
                    // 付费系列列表
                    items(uiState.paidSeries.size) { index ->
                        val series = uiState.paidSeries[index]
                        
                        SeriesHeader(
                            series = series,
                            isPurchased = uiState.purchasedSeriesIds.contains(series.id),
                            isExpanded = uiState.expandedSeriesIds.contains(series.id),
                            onToggleExpanded = { viewModel.toggleSeriesExpanded(series.id) },
                            onPurchase = { onNavigateToPayment(series.id) }
                        )
                        
                        // 展开时显示分类
                        if (uiState.expandedSeriesIds.contains(series.id)) {
                            series.categories.forEach { category ->
                                CategoryCard(
                                    category = category,
                                    isPurchased = uiState.purchasedCategoryIds.contains(category.id),
                                    isExpanded = uiState.expandedCategoryIds.contains(category.id),
                                    onToggleExpanded = { viewModel.toggleCategoryExpanded(category.id) },
                                    onVideoClick = { videoId ->
                                        if (uiState.purchasedCategoryIds.contains(category.id)) {
                                            onNavigateToVideoPlayer(videoId)
                                        } else {
                                            onNavigateToPayment(category.id)
                                        }
                                    },
                                    onPurchase = { onNavigateToPayment(category.id) }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 搜索弹窗
    if (uiState.showSearch) {
        SearchDialog(
            searchQuery = uiState.searchQuery,
            searchResults = uiState.searchResults,
            onQueryChange = { viewModel.updateSearchQuery(it) },
            onDismiss = { viewModel.toggleSearch() },
            onResultClick = { videoId ->
                viewModel.toggleSearch()
                onNavigateToVideoPlayer(videoId)
            }
        )
    }
}

@Composable
private fun SearchDialog(
    searchQuery: String,
    searchResults: List<Any>, // TODO: 定义搜索结果数据类型
    onQueryChange: (String) -> Unit,
    onDismiss: () -> Unit,
    onResultClick: (String) -> Unit
) {
    // TODO: 实现搜索弹窗
    // 占屏幕60%高度
    // 搜索输入框 + 结果列表
}

@Preview(showBackground = true)
@Composable
fun HomeScreenPreview() {
    ShuimuVideoCourseTheme {
        HomeScreen()
    }
}
