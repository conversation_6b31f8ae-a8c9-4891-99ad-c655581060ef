package com.shuimu.videocourse.presentation.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.videocourse.presentation.components.components_full.basic.ShuimuCard
import com.shuimu.videocourse.presentation.components.components_full.basic.ProgressBar
import com.shuimu.videocourse.presentation.components.components_full.display.VideoItem
import com.shuimu.videocourse.presentation.components.components_full.status.LoadingIndicator
import com.shuimu.videocourse.presentation.components.components_full.status.EmptyState
import com.shuimu.videocourse.presentation.viewmodel.CacheManagementViewModel
import com.shuimu.videocourse.presentation.theme.ShuimuVideoCourseTheme

/**
 * 缓存管理页面 - 对应06-缓存管理页面.html
 * 
 * 功能特性：
 * - 存储信息显示（已用空间/总空间，进度条展示）
 * - 视频列表（缓存视频紧凑布局）
 * - 批量删除功能
 * - 下载控制（WiFi自动缓存，手动缓存选择）
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CacheManagementScreen(
    onNavigateBack: () -> Unit = {},
    viewModel: CacheManagementViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(Unit) {
        viewModel.loadCacheInfo()
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部导航栏
        TopAppBar(
            title = { Text("缓存管理") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                // 批量删除按钮
                if (uiState.selectedVideos.isNotEmpty()) {
                    TextButton(
                        onClick = { viewModel.deleteSelectedVideos() }
                    ) {
                        Text("删除(${uiState.selectedVideos.size})")
                    }
                }
            }
        )
        
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    LoadingIndicator(message = "加载缓存信息中...")
                }
            }
            
            else -> {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 存储空间信息
                    item {
                        StorageInfoCard(
                            usedSpace = uiState.usedSpace,
                            totalSpace = uiState.totalSpace,
                            usagePercentage = uiState.usagePercentage
                        )
                    }
                    
                    // 缓存设置
                    item {
                        CacheSettingsCard(
                            autoDownloadOnWifi = uiState.autoDownloadOnWifi,
                            onAutoDownloadToggle = { viewModel.toggleAutoDownload() }
                        )
                    }
                    
                    // 缓存视频列表
                    item {
                        Text(
                            text = "已缓存视频 (${uiState.cachedVideos.size})",
                            style = MaterialTheme.typography.titleMedium,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                    
                    if (uiState.cachedVideos.isEmpty()) {
                        item {
                            EmptyState(
                                message = "暂无缓存视频",
                                actionText = "去首页选择视频",
                                onAction = { /* 导航到首页 */ }
                            )
                        }
                    } else {
                        items(uiState.cachedVideos) { video ->
                            VideoItem(
                                video = video,
                                isSelected = uiState.selectedVideos.contains(video.id),
                                showCheckbox = true,
                                onSelectionChange = { isSelected ->
                                    viewModel.toggleVideoSelection(video.id, isSelected)
                                },
                                onClick = { /* 播放视频 */ }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun StorageInfoCard(
    usedSpace: Long,
    totalSpace: Long,
    usagePercentage: Float
) {
    ShuimuCard {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "存储空间",
                style = MaterialTheme.typography.titleMedium
            )
            
            ProgressBar(
                progress = usagePercentage,
                modifier = Modifier.fillMaxWidth()
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "已用: ${formatFileSize(usedSpace)}",
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "总计: ${formatFileSize(totalSpace)}",
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

@Composable
private fun CacheSettingsCard(
    autoDownloadOnWifi: Boolean,
    onAutoDownloadToggle: () -> Unit
) {
    ShuimuCard {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "WiFi自动缓存",
                    style = MaterialTheme.typography.titleSmall
                )
                Text(
                    text = "在WiFi环境下自动缓存新购买的视频",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Switch(
                checked = autoDownloadOnWifi,
                onCheckedChange = { onAutoDownloadToggle() }
            )
        }
    }
}

private fun formatFileSize(bytes: Long): String {
    val kb = bytes / 1024.0
    val mb = kb / 1024.0
    val gb = mb / 1024.0
    
    return when {
        gb >= 1 -> String.format("%.1f GB", gb)
        mb >= 1 -> String.format("%.1f MB", mb)
        else -> String.format("%.1f KB", kb)
    }
}

@Preview(showBackground = true)
@Composable
fun CacheManagementScreenPreview() {
    ShuimuVideoCourseTheme {
        CacheManagementScreen()
    }
}
