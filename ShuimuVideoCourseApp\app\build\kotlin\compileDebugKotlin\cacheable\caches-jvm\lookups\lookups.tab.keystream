  Application android.app  onCreate android.app.Application  isSystemInDarkTheme androidx.compose.foundation  Boolean androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  darkColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ComposableFunction0 !androidx.compose.runtime.internal  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  White ,androidx.compose.ui.graphics.Color.Companion  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  Application com.shuimu.videocourse  HiltAndroidApp com.shuimu.videocourse  ShuimuVideoCourseApplication com.shuimu.videocourse  
initializeApp 3com.shuimu.videocourse.ShuimuVideoCourseApplication  Boolean )com.shuimu.videocourse.presentation.theme  Color )com.shuimu.videocourse.presentation.theme  
Composable )com.shuimu.videocourse.presentation.theme  DarkColorScheme )com.shuimu.videocourse.presentation.theme  
FontFamily )com.shuimu.videocourse.presentation.theme  
FontWeight )com.shuimu.videocourse.presentation.theme  LightColorScheme )com.shuimu.videocourse.presentation.theme  
MaterialTheme )com.shuimu.videocourse.presentation.theme  ShuimuGreen )com.shuimu.videocourse.presentation.theme  ShuimuOrange )com.shuimu.videocourse.presentation.theme  
ShuimuPrimary )com.shuimu.videocourse.presentation.theme  ShuimuSecondary )com.shuimu.videocourse.presentation.theme  ShuimuTheme )com.shuimu.videocourse.presentation.theme  ShuimuVideoCourseTheme )com.shuimu.videocourse.presentation.theme  
Typography )com.shuimu.videocourse.presentation.theme  Unit )com.shuimu.videocourse.presentation.theme  darkColorScheme )com.shuimu.videocourse.presentation.theme  lightColorScheme )com.shuimu.videocourse.presentation.theme  HiltAndroidApp dagger.hilt.android  sp 
kotlin.Double  
unaryMinus 
kotlin.Double                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              