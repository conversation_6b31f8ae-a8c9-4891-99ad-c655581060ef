{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-72:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e763ae93d7b51c12c0058b9fd0f99eb\\transformed\\material3-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,415,531,626,722,835,973,1093,1243,1328,1431,1522,1619,1749,1869,1977,2122,2268,2398,2587,2714,2832,2954,3080,3172,3267,3395,3521,3620,3722,3834,3980,4132,4246,4346,4422,4522,4621,4731,4817,4907,5012,5092,5176,5276,5376,5471,5573,5659,5761,5859,5963,6078,6158,6258", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,109,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "168,286,410,526,621,717,830,968,1088,1238,1323,1426,1517,1614,1744,1864,1972,2117,2263,2393,2582,2709,2827,2949,3075,3167,3262,3390,3516,3615,3717,3829,3975,4127,4241,4341,4417,4517,4616,4726,4812,4902,5007,5087,5171,5271,5371,5466,5568,5654,5756,5854,5958,6073,6153,6253,6347"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10271,10389,10507,10631,10747,10842,10938,11051,11189,11309,11459,11544,11647,11738,11835,11965,12085,12193,12338,12484,12614,12803,12930,13048,13170,13296,13388,13483,13611,13737,13836,13938,14050,14196,14348,14462,14562,14638,14738,14837,14947,15033,15123,15228,15308,15392,15492,15592,15687,15789,15875,15977,16075,16179,16294,16374,16474", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,109,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "10384,10502,10626,10742,10837,10933,11046,11184,11304,11454,11539,11642,11733,11830,11960,12080,12188,12333,12479,12609,12798,12925,13043,13165,13291,13383,13478,13606,13732,13831,13933,14045,14191,14343,14457,14557,14633,14733,14832,14942,15028,15118,15223,15303,15387,15487,15587,15682,15784,15870,15972,16070,16174,16289,16369,16469,16563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\806eeec989dab5f6c89001161fc1ea32\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "749,855,953,1063,1149,1251,1372,1450,1527,1618,1711,1806,1900,2000,2093,2188,2282,2373,2464,2545,2650,2752,2850,2960,3063,3172,3330,17377", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "850,948,1058,1144,1246,1367,1445,1522,1613,1706,1801,1895,1995,2088,2183,2277,2368,2459,2540,2645,2747,2845,2955,3058,3167,3325,3426,17454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d8a412d674739c339798b6565d8f6383\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,993,1078,1151,1244,1319,1394,1475,1541", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,988,1073,1146,1239,1314,1389,1470,1536,1656"}, "to": {"startLines": "53,54,55,57,58,126,127,191,192,193,194,196,197,198,199,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4184,4279,4362,4531,4629,10096,10174,17037,17126,17211,17292,17459,17532,17625,17700,17876,17957,18023", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "4274,4357,4450,4624,4713,10169,10266,17121,17206,17287,17372,17527,17620,17695,17770,17952,18018,18138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\629a8056d9c025bf7ae40522f2b21f50\\transformed\\media3-session-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,246,316,424,523,623,705,793,896,986,1055,1157,1254,1353,1445,1547,1625,1727,1795,1862,1938,2017,2104", "endColumns": "75,114,69,107,98,99,81,87,102,89,68,101,96,98,91,101,77,101,67,66,75,78,86,91", "endOffsets": "126,241,311,419,518,618,700,788,891,981,1050,1152,1249,1348,1440,1542,1620,1722,1790,1857,1933,2012,2099,2191"}, "to": {"startLines": "56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4455,4718,4833,4903,5011,5110,5210,5292,5380,5483,5573,5642,5744,5841,5940,6032,6134,6212,16568,16636,16703,16779,16858,16945", "endColumns": "75,114,69,107,98,99,81,87,102,89,68,101,96,98,91,101,77,101,67,66,75,78,86,91", "endOffsets": "4526,4828,4898,5006,5105,5205,5287,5375,5478,5568,5637,5739,5836,5935,6027,6129,6207,6309,16631,16698,16774,16853,16940,17032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\93a8666d95f1b2e5574541723f67c731\\transformed\\core-1.15.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "46,47,48,49,50,51,52,200", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3431,3529,3632,3737,3838,3951,4057,17775", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3524,3627,3732,3833,3946,4052,4179,17871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d9611cabb0369607a576bde775e2495\\transformed\\foundation-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "204,205", "startColumns": "4,4", "startOffsets": "18143,18228", "endColumns": "84,85", "endOffsets": "18223,18309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b26b2c6972d7ec14d495070bc19fa7d\\transformed\\media3-ui-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,699,782,863,946,1041,1141,1210,1273,1359,1445,1510,1574,1638,1706,1819,1935,2047,2120,2204,2273,2342,2426,2508,2575,2638,2691,2753,2807,2868,2928,2995,3058,3128,3189,3251,3317,3377,3437,3511,3585,3638", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,59,59,73,73,52,74", "endOffsets": "281,488,694,777,858,941,1036,1136,1205,1268,1354,1440,1505,1569,1633,1701,1814,1930,2042,2115,2199,2268,2337,2421,2503,2570,2633,2686,2748,2802,2863,2923,2990,3053,3123,3184,3246,3312,3372,3432,3506,3580,3633,3708"}, "to": {"startLines": "2,11,15,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,543,6314,6397,6478,6561,6656,6756,6825,6888,6974,7060,7125,7189,7253,7321,7434,7550,7662,7735,7819,7888,7957,8041,8123,8190,9021,9074,9136,9190,9251,9311,9378,9441,9511,9572,9634,9700,9760,9820,9894,9968,10021", "endLines": "10,14,18,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,59,59,73,73,52,74", "endOffsets": "331,538,744,6392,6473,6556,6651,6751,6820,6883,6969,7055,7120,7184,7248,7316,7429,7545,7657,7730,7814,7883,7952,8036,8118,8185,8248,9069,9131,9185,9246,9306,9373,9436,9506,9567,9629,9695,9755,9815,9889,9963,10016,10091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2936cbfd3caa73223b489c015e1e2446\\transformed\\media3-exoplayer-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8253,8321,8387,8458,8526,8622,8690,8813,8934", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "8316,8382,8453,8521,8617,8685,8808,8929,9016"}}]}]}