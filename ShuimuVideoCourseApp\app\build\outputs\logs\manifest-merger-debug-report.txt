-- Merging decision tree log ---
manifest
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:1-45:12
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:1-45:12
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:1-45:12
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:1-45:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a917e0c39b1ae2acbf65e1ecc7ecd325\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c8f97c563752f73ae37d418ae728783\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d31d2e8c126cbf0f2454d0c65f9bde2\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a15c2856ac6517c35961a92fd0e80d18\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d6314c12ce0c28d76a26cc8c2e03ba2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\806eeec989dab5f6c89001161fc1ea32\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf7a771da62f0446ca886eef740586ae\transformed\accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b26b2c6972d7ec14d495070bc19fa7d\transformed\media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\69a817e39a94816bf5bba4362442d8e0\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\629a8056d9c025bf7ae40522f2b21f50\transformed\media3-session-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ebb963de48c5328cd5273d1916a5639\transformed\media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cddf25cf298fa3fef0277fc7a5fc2caa\transformed\media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\888fe0d4495e081eb98e0b8c35d79fbe\transformed\media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2765e1cff8f37fd0eba05f0887114bb\transformed\media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4a959be3416c8a1e7172da997595afb\transformed\media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6606eef68d8fbdde6794fdcad952e160\transformed\media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\04c0bb47c04c3eca8537e2056b6ef4cb\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e205f564bff13b70d8a62b9c1e948c4e\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2936cbfd3caa73223b489c015e1e2446\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fe85024ea9661e36e531445a887537a\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f9370e5ba68405e7e49f8d484829dc3\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\437bf329ce3f0aa7701aabd6648eaa6e\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5880d0773130fb7dc2203d987ac0eed\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\5927188613bf648d36c0e8e1ace96d36\transformed\navigation-common-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\79940b36c0fc4498c4e3ccf1c93d915e\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd3a1517d856370044f8f47d600b242b\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a668c450dd4c8deae3f274a7ccd6f2e\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c22f2f971f643247794be8132aca32d\transformed\navigation-compose-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf429372a58d20b519cf6f50fc16016\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\07fadab8681af489382ed4889d77e8b6\transformed\hilt-android-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0bf7504ba5a2e8560ab8846d4907f54\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e763ae93d7b51c12c0058b9fd0f99eb\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2fcb58cba62ebd293ca68832bd7e8fc\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\720e6778697749371f43d95f7a98f1cc\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eb21a80efb249edc8fb0806189e172d\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\717c66498756218dc7aae3aa6823b049\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ddc885c109387c4ab78bdc5d9dbecae8\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d9611cabb0369607a576bde775e2495\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca9cd9f709bd269e3ff8a1ce079c8a8b\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\620eeb750694324e6f253fc30a47b6e6\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c08129cee903037eb725dc641060def2\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\437f746a0c9dfdc8046b8dee5c6686b9\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\57f78c517ad36ae9f6f15f212e5efd35\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ce4713c03e98d475e2c0fd212464d84\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b499c1337a264aa344a20a2174939e0c\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffbc5c7141168efc0a42bc6d31641018\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4a45dacf4afdd10bf6bb198feb71a1e\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e606b4996b401bdae31cb0bd7c71944\transformed\activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27a3e426d46e5f6e9cd4dee4e6cf0ce2\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57f06f2d58e8896fcc22c03ba7d554d8\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb1d558c5dbffdbed9a85ecb55752d9e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5786b0b1c6df581ff3a91139a23140f9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\038764e41c73ba0396f59bfd9f602e48\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\604be2899c02481aaf8c384f37153f5c\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a517754e55e109038acec685f9fd159\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa93d5acfdc661a1edf197bac1dcd45\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f96a067578d573b125b7734edf334cfa\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d6a57288fc64a1d75c955918143c5d1\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\22b46528d318588adf7d0a27a5b48b96\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\03bb52017f9c36296a2a84e64d826748\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a2ac9aa41095a4d336171570de9b984\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e88257ec84070250d41c4d915e4df4af\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69ff9bddba7607ce96f108e01352b973\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d49c473a4ee23d14addf6ebfb9900b1\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8a412d674739c339798b6565d8f6383\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a7d7766cf750451bbe0c160920431a4\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6773774f5e74c4a64e3d2784f67c136a\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\842d1699a37ae14731d7589f3d51b53d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6666b7286731b5c5ce98a6f66c299379\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db04f123c046dc1bca7cfc3b7cc7c6a5\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37af1f41143b7e3d3ba08bd4cbe6dea6\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c8a3e3317a4283a0a92a8df7d66daf8\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bbd5d596432bfb0fe4e78f755ef4f2b\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\989db46b280c4d5d2499ac718f2ee7b8\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\937d5284e9a75efb4da965b4cde5a1fb\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fa9dc26cef25cf9005e89875a61b6b\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf9a01b62618dca0a7ca954a23dc4ffd\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5396a22eb00ee19409308cdc342d8843\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3108720640097a17e3fd9ce7588f1df8\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d2c13d0d124200996fa8b4d266e1ce8\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd32753d1467282d1784a4fe27147741\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d65266acfd044c26b3a02e92cf33955d\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8a21088b6cabfb1345e9450d534bddf\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c77dfab215b01a47798f7f9c8750e61c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26804d38a8c66447e9922c15a8536c7e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f65b72751e89f0ece9d3c78c557f5262\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fc5b444873293e8df2122adc09bb323\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7fc9c4b071d7dc6aceaed142f6d075b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3a84a15a5816c4819b2173f4ec1c84b\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbb6d49ad5c436fc3906a40869dc6283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9f543fdb43b4ef111c8d36bdcbd0eee\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847bf99e7f2dbd7ea76153a00b57f13f\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:2:1-79:12
MERGED from [com.tencent.mm.opensdk:wechat-sdk-android:6.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8628f94d55ac6148f0342b942d2bc3c0\transformed\wechat-sdk-android-6.8.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\b838810945fbeae1b3ad869be5b3c80b\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:29:5-67
MERGED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:29:5-67
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6606eef68d8fbdde6794fdcad952e160\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6606eef68d8fbdde6794fdcad952e160\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2936cbfd3caa73223b489c015e1e2446\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2936cbfd3caa73223b489c015e1e2446\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:30:5-79
MERGED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:30:5-79
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:8:22-77
application
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:10:5-43:19
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:10:5-43:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7fc9c4b071d7dc6aceaed142f6d075b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7fc9c4b071d7dc6aceaed142f6d075b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbb6d49ad5c436fc3906a40869dc6283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbb6d49ad5c436fc3906a40869dc6283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:33:5-77:19
MERGED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:33:5-77:19
	android:extractNativeLibs
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:18:9-35
	android:label
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:16:9-41
	android:fullBackupContent
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:14:9-54
	android:roundIcon
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:17:9-63
	tools:targetApi
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:20:9-29
	android:icon
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:15:9-58
	android:allowBackup
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:12:9-35
	android:theme
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:19:9-58
	android:dataExtractionRules
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:13:9-65
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:11:9-53
activity#com.shuimu.videocourse.presentation.MainActivity
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:21:9-30:20
	android:label
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:24:13-45
	android:exported
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:23:13-36
	android:theme
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:25:13-62
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:22:13-54
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:26:13-29:29
action#android.intent.action.MAIN
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:17-69
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:17-77
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:27-74
activity#com.shuimu.videocourse.presentation.components.basic.BadgeTestActivity
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:33:9-42:20
	android:label
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:36:13-33
	android:exported
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:37:13-62
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:34:13-76
uses-sdk
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a917e0c39b1ae2acbf65e1ecc7ecd325\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a917e0c39b1ae2acbf65e1ecc7ecd325\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c8f97c563752f73ae37d418ae728783\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c8f97c563752f73ae37d418ae728783\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d31d2e8c126cbf0f2454d0c65f9bde2\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d31d2e8c126cbf0f2454d0c65f9bde2\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a15c2856ac6517c35961a92fd0e80d18\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a15c2856ac6517c35961a92fd0e80d18\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d6314c12ce0c28d76a26cc8c2e03ba2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d6314c12ce0c28d76a26cc8c2e03ba2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\806eeec989dab5f6c89001161fc1ea32\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\806eeec989dab5f6c89001161fc1ea32\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf7a771da62f0446ca886eef740586ae\transformed\accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf7a771da62f0446ca886eef740586ae\transformed\accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b26b2c6972d7ec14d495070bc19fa7d\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b26b2c6972d7ec14d495070bc19fa7d\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\69a817e39a94816bf5bba4362442d8e0\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\69a817e39a94816bf5bba4362442d8e0\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\629a8056d9c025bf7ae40522f2b21f50\transformed\media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\629a8056d9c025bf7ae40522f2b21f50\transformed\media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ebb963de48c5328cd5273d1916a5639\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ebb963de48c5328cd5273d1916a5639\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cddf25cf298fa3fef0277fc7a5fc2caa\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cddf25cf298fa3fef0277fc7a5fc2caa\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\888fe0d4495e081eb98e0b8c35d79fbe\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\888fe0d4495e081eb98e0b8c35d79fbe\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2765e1cff8f37fd0eba05f0887114bb\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2765e1cff8f37fd0eba05f0887114bb\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4a959be3416c8a1e7172da997595afb\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4a959be3416c8a1e7172da997595afb\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6606eef68d8fbdde6794fdcad952e160\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6606eef68d8fbdde6794fdcad952e160\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\04c0bb47c04c3eca8537e2056b6ef4cb\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\04c0bb47c04c3eca8537e2056b6ef4cb\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e205f564bff13b70d8a62b9c1e948c4e\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e205f564bff13b70d8a62b9c1e948c4e\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2936cbfd3caa73223b489c015e1e2446\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2936cbfd3caa73223b489c015e1e2446\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fe85024ea9661e36e531445a887537a\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fe85024ea9661e36e531445a887537a\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f9370e5ba68405e7e49f8d484829dc3\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f9370e5ba68405e7e49f8d484829dc3\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\437bf329ce3f0aa7701aabd6648eaa6e\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\437bf329ce3f0aa7701aabd6648eaa6e\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5880d0773130fb7dc2203d987ac0eed\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5880d0773130fb7dc2203d987ac0eed\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\5927188613bf648d36c0e8e1ace96d36\transformed\navigation-common-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\5927188613bf648d36c0e8e1ace96d36\transformed\navigation-common-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\79940b36c0fc4498c4e3ccf1c93d915e\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\79940b36c0fc4498c4e3ccf1c93d915e\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd3a1517d856370044f8f47d600b242b\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd3a1517d856370044f8f47d600b242b\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a668c450dd4c8deae3f274a7ccd6f2e\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a668c450dd4c8deae3f274a7ccd6f2e\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c22f2f971f643247794be8132aca32d\transformed\navigation-compose-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c22f2f971f643247794be8132aca32d\transformed\navigation-compose-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf429372a58d20b519cf6f50fc16016\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf429372a58d20b519cf6f50fc16016\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\07fadab8681af489382ed4889d77e8b6\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\07fadab8681af489382ed4889d77e8b6\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0bf7504ba5a2e8560ab8846d4907f54\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0bf7504ba5a2e8560ab8846d4907f54\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e763ae93d7b51c12c0058b9fd0f99eb\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e763ae93d7b51c12c0058b9fd0f99eb\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2fcb58cba62ebd293ca68832bd7e8fc\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2fcb58cba62ebd293ca68832bd7e8fc\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\720e6778697749371f43d95f7a98f1cc\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\720e6778697749371f43d95f7a98f1cc\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eb21a80efb249edc8fb0806189e172d\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eb21a80efb249edc8fb0806189e172d\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\717c66498756218dc7aae3aa6823b049\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\717c66498756218dc7aae3aa6823b049\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ddc885c109387c4ab78bdc5d9dbecae8\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ddc885c109387c4ab78bdc5d9dbecae8\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d9611cabb0369607a576bde775e2495\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d9611cabb0369607a576bde775e2495\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca9cd9f709bd269e3ff8a1ce079c8a8b\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca9cd9f709bd269e3ff8a1ce079c8a8b\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\620eeb750694324e6f253fc30a47b6e6\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\620eeb750694324e6f253fc30a47b6e6\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c08129cee903037eb725dc641060def2\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c08129cee903037eb725dc641060def2\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\437f746a0c9dfdc8046b8dee5c6686b9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\437f746a0c9dfdc8046b8dee5c6686b9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\57f78c517ad36ae9f6f15f212e5efd35\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\57f78c517ad36ae9f6f15f212e5efd35\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ce4713c03e98d475e2c0fd212464d84\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ce4713c03e98d475e2c0fd212464d84\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b499c1337a264aa344a20a2174939e0c\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b499c1337a264aa344a20a2174939e0c\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffbc5c7141168efc0a42bc6d31641018\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffbc5c7141168efc0a42bc6d31641018\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4a45dacf4afdd10bf6bb198feb71a1e\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4a45dacf4afdd10bf6bb198feb71a1e\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e606b4996b401bdae31cb0bd7c71944\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e606b4996b401bdae31cb0bd7c71944\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27a3e426d46e5f6e9cd4dee4e6cf0ce2\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27a3e426d46e5f6e9cd4dee4e6cf0ce2\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57f06f2d58e8896fcc22c03ba7d554d8\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57f06f2d58e8896fcc22c03ba7d554d8\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb1d558c5dbffdbed9a85ecb55752d9e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb1d558c5dbffdbed9a85ecb55752d9e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5786b0b1c6df581ff3a91139a23140f9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5786b0b1c6df581ff3a91139a23140f9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\038764e41c73ba0396f59bfd9f602e48\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\038764e41c73ba0396f59bfd9f602e48\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\604be2899c02481aaf8c384f37153f5c\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\604be2899c02481aaf8c384f37153f5c\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a517754e55e109038acec685f9fd159\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a517754e55e109038acec685f9fd159\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa93d5acfdc661a1edf197bac1dcd45\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa93d5acfdc661a1edf197bac1dcd45\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f96a067578d573b125b7734edf334cfa\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f96a067578d573b125b7734edf334cfa\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d6a57288fc64a1d75c955918143c5d1\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d6a57288fc64a1d75c955918143c5d1\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\22b46528d318588adf7d0a27a5b48b96\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\22b46528d318588adf7d0a27a5b48b96\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\03bb52017f9c36296a2a84e64d826748\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\03bb52017f9c36296a2a84e64d826748\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a2ac9aa41095a4d336171570de9b984\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a2ac9aa41095a4d336171570de9b984\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e88257ec84070250d41c4d915e4df4af\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e88257ec84070250d41c4d915e4df4af\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69ff9bddba7607ce96f108e01352b973\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69ff9bddba7607ce96f108e01352b973\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d49c473a4ee23d14addf6ebfb9900b1\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d49c473a4ee23d14addf6ebfb9900b1\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8a412d674739c339798b6565d8f6383\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8a412d674739c339798b6565d8f6383\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a7d7766cf750451bbe0c160920431a4\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a7d7766cf750451bbe0c160920431a4\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6773774f5e74c4a64e3d2784f67c136a\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6773774f5e74c4a64e3d2784f67c136a\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\842d1699a37ae14731d7589f3d51b53d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\842d1699a37ae14731d7589f3d51b53d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6666b7286731b5c5ce98a6f66c299379\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6666b7286731b5c5ce98a6f66c299379\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db04f123c046dc1bca7cfc3b7cc7c6a5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db04f123c046dc1bca7cfc3b7cc7c6a5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37af1f41143b7e3d3ba08bd4cbe6dea6\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37af1f41143b7e3d3ba08bd4cbe6dea6\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c8a3e3317a4283a0a92a8df7d66daf8\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c8a3e3317a4283a0a92a8df7d66daf8\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bbd5d596432bfb0fe4e78f755ef4f2b\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bbd5d596432bfb0fe4e78f755ef4f2b\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\989db46b280c4d5d2499ac718f2ee7b8\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\989db46b280c4d5d2499ac718f2ee7b8\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\937d5284e9a75efb4da965b4cde5a1fb\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\937d5284e9a75efb4da965b4cde5a1fb\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fa9dc26cef25cf9005e89875a61b6b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fa9dc26cef25cf9005e89875a61b6b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf9a01b62618dca0a7ca954a23dc4ffd\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf9a01b62618dca0a7ca954a23dc4ffd\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5396a22eb00ee19409308cdc342d8843\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5396a22eb00ee19409308cdc342d8843\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3108720640097a17e3fd9ce7588f1df8\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3108720640097a17e3fd9ce7588f1df8\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d2c13d0d124200996fa8b4d266e1ce8\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d2c13d0d124200996fa8b4d266e1ce8\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd32753d1467282d1784a4fe27147741\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd32753d1467282d1784a4fe27147741\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d65266acfd044c26b3a02e92cf33955d\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d65266acfd044c26b3a02e92cf33955d\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8a21088b6cabfb1345e9450d534bddf\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8a21088b6cabfb1345e9450d534bddf\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c77dfab215b01a47798f7f9c8750e61c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c77dfab215b01a47798f7f9c8750e61c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26804d38a8c66447e9922c15a8536c7e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26804d38a8c66447e9922c15a8536c7e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f65b72751e89f0ece9d3c78c557f5262\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f65b72751e89f0ece9d3c78c557f5262\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fc5b444873293e8df2122adc09bb323\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fc5b444873293e8df2122adc09bb323\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7fc9c4b071d7dc6aceaed142f6d075b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7fc9c4b071d7dc6aceaed142f6d075b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3a84a15a5816c4819b2173f4ec1c84b\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3a84a15a5816c4819b2173f4ec1c84b\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbb6d49ad5c436fc3906a40869dc6283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbb6d49ad5c436fc3906a40869dc6283\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9f543fdb43b4ef111c8d36bdcbd0eee\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9f543fdb43b4ef111c8d36bdcbd0eee\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847bf99e7f2dbd7ea76153a00b57f13f\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847bf99e7f2dbd7ea76153a00b57f13f\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:7:5-44
MERGED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:7:5-44
MERGED from [com.tencent.mm.opensdk:wechat-sdk-android:6.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8628f94d55ac6148f0342b942d2bc3c0\transformed\wechat-sdk-android-6.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.tencent.mm.opensdk:wechat-sdk-android:6.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8628f94d55ac6148f0342b942d2bc3c0\transformed\wechat-sdk-android-6.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\b838810945fbeae1b3ad869be5b3c80b\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\b838810945fbeae1b3ad869be5b3c80b\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7fc9c4b071d7dc6aceaed142f6d075b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7fc9c4b071d7dc6aceaed142f6d075b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.shuimu.videocourse.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.shuimu.videocourse.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
queries
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:9:5-20:15
intent#action:name:*+data:scheme:tbopen
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:10:9-14:18
action#*
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:11:13-40
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:11:21-37
data
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:13:13-45
	android:scheme
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:13:19-42
package#com.eg.android.AlipayGphone
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:16:9-63
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:16:18-60
package#com.eg.android.AlipayGphoneRC
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:17:9-65
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:17:18-62
package#hk.alipay.wallet
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:18:9-52
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:18:18-49
package#hk.alipay.walletRC
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:19:9-54
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:19:18-51
supports-screens
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:22:5-27:39
	android:largeScreens
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:24:9-36
	android:smallScreens
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:27:9-36
	android:normalScreens
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:25:9-37
	android:resizeable
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:26:9-34
	android:anyDensity
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:23:9-34
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:31:5-76
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:31:22-73
uses-library#org.apache.http.legacy
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:34:9-36:40
	android:required
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:36:13-37
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:35:13-50
activity#com.alipay.sdk.app.H5PayActivity
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:38:9-43:20
	android:exported
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:41:13-37
	android:configChanges
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:40:13-167
	android:theme
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:42:13-60
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:39:13-60
activity#com.alipay.sdk.app.H5AuthActivity
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:44:9-49:20
	android:exported
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:47:13-37
	android:configChanges
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:46:13-167
	android:theme
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:48:13-60
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:45:13-61
activity#com.alipay.sdk.app.PayResultActivity
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:50:9-56:20
	android:launchMode
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:54:13-48
	android:exported
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:53:13-36
	android:configChanges
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:52:13-167
	android:theme
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:55:13-72
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:51:13-64
activity#com.alipay.sdk.app.AlipayResultActivity
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:57:9-63:20
	android:launchMode
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:61:13-44
	android:exported
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:60:13-36
	android:configChanges
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:59:13-167
	android:theme
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:62:13-72
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:58:13-67
activity#com.alipay.sdk.app.H5OpenAuthActivity
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:64:9-70:20
	android:screenOrientation
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:68:13-47
	android:windowSoftInputMode
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:69:13-67
	android:exported
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:67:13-37
	android:configChanges
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:66:13-167
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:65:13-65
activity#com.alipay.sdk.app.APayEntranceActivity
ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:71:9-76:20
	android:exported
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:74:13-37
	android:configChanges
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:73:13-167
	android:theme
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:75:13-72
	android:name
		ADDED from [com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:72:13-67
