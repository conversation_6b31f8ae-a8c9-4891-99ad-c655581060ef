{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-72:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\806eeec989dab5f6c89001161fc1ea32\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "988,1095,1196,1307,1393,1501,1619,1698,1775,1866,1959,2057,2151,2251,2344,2439,2537,2628,2719,2803,2908,3016,3115,3221,3333,3436,3602,17536", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "1090,1191,1302,1388,1496,1614,1693,1770,1861,1954,2052,2146,2246,2339,2434,2532,2623,2714,2798,2903,3011,3110,3216,3328,3431,3597,3695,17614"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d8a412d674739c339798b6565d8f6383\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,1001,1088,1160,1238,1314,1389,1467,1535", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,996,1083,1155,1233,1309,1384,1462,1530,1644"}, "to": {"startLines": "57,58,59,61,62,130,131,195,196,197,198,200,201,202,203,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4437,4532,4616,4785,4888,10308,10387,17195,17285,17366,17449,17619,17691,17769,17845,18021,18099,18167", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "4527,4611,4706,4883,4975,10382,10476,17280,17361,17444,17531,17686,17764,17840,17915,18094,18162,18276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\629a8056d9c025bf7ae40522f2b21f50\\transformed\\media3-session-1.4.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,240,316,420,513,591,668,758,856,934,1006,1106,1189,1283,1366,1462,1542,1648,1721,1790,1877,1968,2061", "endColumns": "73,110,75,103,92,77,76,89,97,77,71,99,82,93,82,95,79,105,72,68,86,90,92,105", "endOffsets": "124,235,311,415,508,586,663,753,851,929,1001,1101,1184,1278,1361,1457,1537,1643,1716,1785,1872,1963,2056,2162"}, "to": {"startLines": "60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,189,190,191,192,193,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4711,4980,5091,5167,5271,5364,5442,5519,5609,5707,5785,5857,5957,6040,6134,6217,6313,6393,16676,16749,16818,16905,16996,17089", "endColumns": "73,110,75,103,92,77,76,89,97,77,71,99,82,93,82,95,79,105,72,68,86,90,92,105", "endOffsets": "4780,5086,5162,5266,5359,5437,5514,5604,5702,5780,5852,5952,6035,6129,6212,6308,6388,6494,16744,16813,16900,16991,17084,17190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d9611cabb0369607a576bde775e2495\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "208,209", "startColumns": "4,4", "startOffsets": "18281,18365", "endColumns": "83,86", "endOffsets": "18360,18447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\93a8666d95f1b2e5574541723f67c731\\transformed\\core-1.15.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "50,51,52,53,54,55,56,204", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3700,3796,3898,3999,4097,4207,4315,17920", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3791,3893,3994,4092,4202,4310,4432,18016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e763ae93d7b51c12c0058b9fd0f99eb\\transformed\\material3-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4635,4722,4809,4910,4990,5076,5173,5276,5369,5466,5554,5659,5756,5855,5975,6055,6157", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4630,4717,4804,4905,4985,5071,5168,5271,5364,5461,5549,5654,5751,5850,5970,6050,6152,6245"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10481,10596,10711,10821,10936,11034,11129,11241,11376,11492,11644,11729,11830,11922,12019,12135,12257,12363,12496,12629,12763,12927,13055,13179,13309,13429,13522,13619,13740,13863,13961,14064,14173,14314,14463,14572,14672,14756,14850,14945,15061,15148,15235,15336,15416,15502,15599,15702,15795,15892,15980,16085,16182,16281,16401,16481,16583", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "10591,10706,10816,10931,11029,11124,11236,11371,11487,11639,11724,11825,11917,12014,12130,12252,12358,12491,12624,12758,12922,13050,13174,13304,13424,13517,13614,13735,13858,13956,14059,14168,14309,14458,14567,14667,14751,14845,14940,15056,15143,15230,15331,15411,15497,15594,15697,15790,15887,15975,16080,16177,16276,16396,16476,16578,16671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b26b2c6972d7ec14d495070bc19fa7d\\transformed\\media3-ui-1.4.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2112,2229,2350,2424,2506,2579,2661,2761,2860,2927,2992,3045,3103,3151,3212,3284,3358,3421,3494,3559,3619,3684,3736,3800,3878,3956,4010", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,51,63,77,77,53,65", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2107,2224,2345,2419,2501,2574,2656,2756,2855,2922,2987,3040,3098,3146,3207,3279,3353,3416,3489,3554,3614,3679,3731,3795,3873,3951,4005,4071"}, "to": {"startLines": "2,11,17,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,674,6499,6580,6660,6742,6845,6944,7023,7088,7179,7273,7343,7409,7474,7551,7673,7790,7911,7985,8067,8140,8222,8322,8421,8488,9224,9277,9335,9383,9444,9516,9590,9653,9726,9791,9851,9916,9968,10032,10110,10188,10242", "endLines": "10,16,22,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,51,63,77,77,53,65", "endOffsets": "338,669,983,6575,6655,6737,6840,6939,7018,7083,7174,7268,7338,7404,7469,7546,7668,7785,7906,7980,8062,8135,8217,8317,8416,8483,8548,9272,9330,9378,9439,9511,9585,9648,9721,9786,9846,9911,9963,10027,10105,10183,10237,10303"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2936cbfd3caa73223b489c015e1e2446\\transformed\\media3-exoplayer-1.4.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "104,105,106,107,108,109,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8553,8630,8692,8756,8827,8904,8978,9062,9144", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "8625,8687,8751,8822,8899,8973,9057,9139,9219"}}]}]}