{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-72:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\93a8666d95f1b2e5574541723f67c731\\transformed\\core-1.15.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "46,47,48,49,50,51,52,200", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3471,3570,3672,3774,3877,3978,4080,17757", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3565,3667,3769,3872,3973,4075,4195,17853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\629a8056d9c025bf7ae40522f2b21f50\\transformed\\media3-session-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,226,293,391,484,577,654,746,841,917,992,1088,1172,1271,1352,1450,1523,1622,1692,1759,1843,1922,2007", "endColumns": "70,99,66,97,92,92,76,91,94,75,74,95,83,98,80,97,72,98,69,66,83,78,84,88", "endOffsets": "121,221,288,386,479,572,649,741,836,912,987,1083,1167,1266,1347,1445,1518,1617,1687,1754,1838,1917,2002,2091"}, "to": {"startLines": "56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4468,4723,4823,4890,4988,5081,5174,5251,5343,5438,5514,5589,5685,5769,5868,5949,6047,6120,16549,16619,16686,16770,16849,16934", "endColumns": "70,99,66,97,92,92,76,91,94,75,74,95,83,98,80,97,72,98,69,66,83,78,84,88", "endOffsets": "4534,4818,4885,4983,5076,5169,5246,5338,5433,5509,5584,5680,5764,5863,5944,6042,6115,6214,16614,16681,16765,16844,16929,17018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d9611cabb0369607a576bde775e2495\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "204,205", "startColumns": "4,4", "startOffsets": "18119,18204", "endColumns": "84,84", "endOffsets": "18199,18284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b26b2c6972d7ec14d495070bc19fa7d\\transformed\\media3-ui-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,691,776,860,941,1034,1130,1206,1272,1361,1450,1517,1581,1643,1716,1832,1948,2066,2137,2220,2289,2365,2453,2540,2604,2669,2722,2784,2832,2893,2953,3015,3079,3145,3202,3266,3331,3384,3447,3524,3601,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "283,492,686,771,855,936,1029,1125,1201,1267,1356,1445,1512,1576,1638,1711,1827,1943,2061,2132,2215,2284,2360,2448,2535,2599,2664,2717,2779,2827,2888,2948,3010,3074,3140,3197,3261,3326,3379,3442,3519,3596,3648,3712"}, "to": {"startLines": "2,11,15,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,547,6219,6304,6388,6469,6562,6658,6734,6800,6889,6978,7045,7109,7171,7244,7360,7476,7594,7665,7748,7817,7893,7981,8068,8132,8956,9009,9071,9119,9180,9240,9302,9366,9432,9489,9553,9618,9671,9734,9811,9888,9940", "endLines": "10,14,18,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "333,542,736,6299,6383,6464,6557,6653,6729,6795,6884,6973,7040,7104,7166,7239,7355,7471,7589,7660,7743,7812,7888,7976,8063,8127,8192,9004,9066,9114,9175,9235,9297,9361,9427,9484,9548,9613,9666,9729,9806,9883,9935,9999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\806eeec989dab5f6c89001161fc1ea32\\transformed\\appcompat-1.7.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "741,849,955,1061,1150,1255,1376,1459,1541,1632,1725,1819,1913,2013,2106,2201,2295,2386,2477,2563,2673,2777,2880,2988,3096,3201,3366,17358", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "844,950,1056,1145,1250,1371,1454,1536,1627,1720,1814,1908,2008,2101,2196,2290,2381,2472,2558,2668,2772,2875,2983,3091,3196,3361,3466,17440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e763ae93d7b51c12c0058b9fd0f99eb\\transformed\\material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4771,4862,4952,5055,5135,5220,5321,5425,5518,5623,5710,5816,5915,6023,6141,6221,6321", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4766,4857,4947,5050,5130,5215,5316,5420,5513,5618,5705,5811,5910,6018,6136,6216,6316,6410"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10189,10327,10464,10583,10717,10834,10933,11049,11191,11312,11454,11539,11645,11739,11840,11969,12098,12209,12338,12465,12595,12775,12897,13017,13139,13270,13365,13460,13593,13740,13837,13942,14052,14179,14311,14418,14519,14596,14699,14799,14905,14996,15086,15189,15269,15354,15455,15559,15652,15757,15844,15950,16049,16157,16275,16355,16455", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "10322,10459,10578,10712,10829,10928,11044,11186,11307,11449,11534,11640,11734,11835,11964,12093,12204,12333,12460,12590,12770,12892,13012,13134,13265,13360,13455,13588,13735,13832,13937,14047,14174,14306,14413,14514,14591,14694,14794,14900,14991,15081,15184,15264,15349,15450,15554,15647,15752,15839,15945,16044,16152,16270,16350,16450,16544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d8a412d674739c339798b6565d8f6383\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,996,1077,1160,1236,1313,1389,1464,1532", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,75,76,75,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,991,1072,1155,1231,1308,1384,1459,1527,1645"}, "to": {"startLines": "53,54,55,57,58,126,127,191,192,193,194,196,197,198,199,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4200,4294,4378,4539,4637,10004,10083,17023,17110,17199,17277,17445,17528,17604,17681,17858,17933,18001", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,75,76,75,74,67,117", "endOffsets": "4289,4373,4463,4632,4718,10078,10184,17105,17194,17272,17353,17523,17599,17676,17752,17928,17996,18114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2936cbfd3caa73223b489c015e1e2446\\transformed\\media3-exoplayer-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8197,8265,8331,8398,8464,8539,8606,8738,8867", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "8260,8326,8393,8459,8534,8601,8733,8862,8951"}}]}]}