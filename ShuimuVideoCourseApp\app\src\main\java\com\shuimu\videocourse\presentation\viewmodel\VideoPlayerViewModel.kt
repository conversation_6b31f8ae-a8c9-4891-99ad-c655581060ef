package com.shuimu.videocourse.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.shuimu.videocourse.domain.usecase.video.GetVideoDetailUseCase
import com.shuimu.videocourse.domain.usecase.video.CacheVideoUseCase
import com.shuimu.videocourse.domain.usecase.video.UpdateProgressUseCase
import com.shuimu.videocourse.domain.model.Video
import com.shuimu.videocourse.domain.model.Learning
import javax.inject.Inject

/**
 * 视频播放页ViewModel
 * 
 * 负责管理：
 * - 视频播放状态
 * - 强制缓存逻辑
 * - 播放进度记录
 * - 防录屏水印
 * - 播放控制
 */
@HiltViewModel
class VideoPlayerViewModel @Inject constructor(
    private val getVideoDetailUseCase: GetVideoDetailUseCase,
    private val cacheVideoUseCase: CacheVideoUseCase,
    private val updateProgressUseCase: UpdateProgressUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(VideoPlayerUiState())
    val uiState: StateFlow<VideoPlayerUiState> = _uiState.asStateFlow()
    
    fun loadVideo(videoId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                getVideoDetailUseCase(videoId).collect { result ->
                    result.fold(
                        onSuccess = { video ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                video = video,
                                error = null
                            )
                            
                            // 检查缓存状态，如果未缓存则开始缓存
                            if (video.localPath == null) {
                                startCaching(video)
                            } else {
                                // 已缓存，可以直接播放
                                initializePlayer(video)
                            }
                        },
                        onFailure = { error ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = error.message ?: "加载视频失败"
                            )
                        }
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载视频失败"
                )
            }
        }
    }
    
    private fun startCaching(video: Video) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isCaching = true)
            
            try {
                cacheVideoUseCase(video.id).collect { result ->
                    result.fold(
                        onSuccess = { cachedVideo ->
                            _uiState.value = _uiState.value.copy(
                                isCaching = false,
                                video = cachedVideo
                            )
                            initializePlayer(cachedVideo)
                        },
                        onFailure = { error ->
                            _uiState.value = _uiState.value.copy(
                                isCaching = false,
                                error = error.message ?: "缓存视频失败"
                            )
                        }
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isCaching = false,
                    error = e.message ?: "缓存视频失败"
                )
            }
        }
    }
    
    private fun initializePlayer(video: Video) {
        // 初始化播放器
        // 设置断点续播位置
        val lastPosition = video.lastWatchPosition ?: 0L
        _uiState.value = _uiState.value.copy(
            currentPosition = lastPosition,
            showWatermark = true,
            watermarkText = generateWatermarkText()
        )
    }
    
    private fun generateWatermarkText(): String {
        // 生成动态水印文本
        return "水幕视频课程 - ${System.currentTimeMillis()}"
    }
    
    fun togglePlayPause() {
        _uiState.value = _uiState.value.copy(
            isPlaying = !_uiState.value.isPlaying
        )
    }
    
    fun seekTo(position: Long) {
        _uiState.value = _uiState.value.copy(currentPosition = position)
    }
    
    fun setPlaybackSpeed(speed: Float) {
        _uiState.value = _uiState.value.copy(playbackSpeed = speed)
    }
    
    fun toggleFullscreen() {
        _uiState.value = _uiState.value.copy(
            isFullscreen = !_uiState.value.isFullscreen
        )
    }
    
    fun saveProgress() {
        val currentState = _uiState.value
        val video = currentState.video ?: return
        
        viewModelScope.launch {
            try {
                updateProgressUseCase(
                    videoId = video.id,
                    position = currentState.currentPosition,
                    duration = currentState.duration
                )
            } catch (e: Exception) {
                // 静默处理进度保存失败
            }
        }
    }
}

/**
 * 视频播放页UI状态
 */
data class VideoPlayerUiState(
    val isLoading: Boolean = false,
    val isCaching: Boolean = false,
    val error: String? = null,
    val video: Video? = null,
    val isPlaying: Boolean = false,
    val currentPosition: Long = 0L,
    val duration: Long = 0L,
    val playbackSpeed: Float = 1.0f,
    val isFullscreen: Boolean = false,
    val showWatermark: Boolean = false,
    val watermarkText: String = "",
    val learningProgress: Learning? = null,
    val relatedVideos: List<Video> = emptyList()
)
