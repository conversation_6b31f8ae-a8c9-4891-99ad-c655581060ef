{"src\\main\\java\\com\\shuimu\\videocourse\\presentation\\theme\\Theme.kt": ["ShuimuTheme:com.shuimu.videocourse.presentation.theme", "ShuimuVideoCourseTheme:com.shuimu.videocourse.presentation.theme"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\shuimu\\videocourse\\ShuimuVideoCourseApplication_GeneratedInjector.java": ["injectShuimuVideoCourseApplication:com.shuimu.videocourse.ShuimuVideoCourseApplication_GeneratedInjector", "ShuimuVideoCourseApplication_GeneratedInjector:com.shuimu.videocourse"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\dagger\\hilt\\internal\\aggregatedroot\\codegen\\_com_shuimu_videocourse_ShuimuVideoCourseApplication.java": ["_com_shuimu_videocourse_ShuimuVideoCourseApplication:dagger.hilt.internal.aggregatedroot.codegen", "<init>:dagger.hilt.internal.aggregatedroot.codegen._com_shuimu_videocourse_ShuimuVideoCourseApplication"], "src\\main\\java\\com\\shuimu\\videocourse\\ShuimuVideoCourseApplication.kt": ["ShuimuVideoCourseApplication:com.shuimu.videocourse", "onCreate:com.shuimu.videocourse.ShuimuVideoCourseApplication", "<init>:com.shuimu.videocourse.ShuimuVideoCourseApplication"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_shuimu_videocourse_ShuimuVideoCourseApplication_GeneratedInjector.java": ["_com_shuimu_videocourse_ShuimuVideoCourseApplication_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_shuimu_videocourse_ShuimuVideoCourseApplication_GeneratedInjector"], "src\\main\\java\\com\\shuimu\\videocourse\\presentation\\theme\\Type.kt": ["Typography:com.shuimu.videocourse.presentation.theme"]}