package com.shuimu.videocourse.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.shuimu.videocourse.domain.usecase.course.GetCourseListUseCase
import com.shuimu.videocourse.domain.usecase.course.SearchCourseUseCase
import com.shuimu.videocourse.domain.usecase.user.GetUserInfoUseCase
import com.shuimu.videocourse.domain.model.Course
import javax.inject.Inject

/**
 * 首页ViewModel
 * 
 * 负责管理：
 * - 课程列表数据
 * - 3层结构展示状态
 * - 搜索功能
 * - 购买状态
 * - 动态价格计算
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val getCourseListUseCase: GetCourseListUseCase,
    private val searchCourseUseCase: SearchCourseUseCase,
    private val getUserInfoUseCase: GetUserInfoUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    init {
        loadCourses()
        loadUserInfo()
    }
    
    fun loadCourses() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                getCourseListUseCase().collect { result ->
                    result.fold(
                        onSuccess = { courses ->
                            val freeSeries = courses.find { it.isFree }
                            val paidSeries = courses.filter { !it.isFree }
                            val bundlePrice = calculateBundlePrice(paidSeries)
                            
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                freeSeries = freeSeries,
                                paidSeries = paidSeries,
                                bundlePrice = bundlePrice,
                                error = null
                            )
                        },
                        onFailure = { error ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = error.message ?: "加载课程失败"
                            )
                        }
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载课程失败"
                )
            }
        }
    }
    
    private fun loadUserInfo() {
        viewModelScope.launch {
            try {
                getUserInfoUseCase().collect { result ->
                    result.fold(
                        onSuccess = { user ->
                            _uiState.value = _uiState.value.copy(
                                isLoggedIn = user != null,
                                purchasedSeriesIds = user?.purchasedSeriesIds ?: emptySet(),
                                purchasedCategoryIds = user?.purchasedCategoryIds ?: emptySet()
                            )
                        },
                        onFailure = {
                            _uiState.value = _uiState.value.copy(isLoggedIn = false)
                        }
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(isLoggedIn = false)
            }
        }
    }
    
    private fun calculateBundlePrice(paidSeries: List<Course>): Double {
        val currentState = _uiState.value
        return paidSeries
            .filter { !currentState.purchasedSeriesIds.contains(it.id) }
            .sumOf { it.price }
    }
    
    fun toggleSeriesExpanded(seriesId: String) {
        val currentExpanded = _uiState.value.expandedSeriesIds.toMutableSet()
        if (currentExpanded.contains(seriesId)) {
            currentExpanded.remove(seriesId)
        } else {
            currentExpanded.add(seriesId)
        }
        _uiState.value = _uiState.value.copy(expandedSeriesIds = currentExpanded)
    }
    
    fun toggleCategoryExpanded(categoryId: String) {
        val currentExpanded = _uiState.value.expandedCategoryIds.toMutableSet()
        if (currentExpanded.contains(categoryId)) {
            currentExpanded.remove(categoryId)
        } else {
            currentExpanded.add(categoryId)
        }
        _uiState.value = _uiState.value.copy(expandedCategoryIds = currentExpanded)
    }
    
    fun toggleSearch() {
        _uiState.value = _uiState.value.copy(
            showSearch = !_uiState.value.showSearch,
            searchQuery = if (!_uiState.value.showSearch) "" else _uiState.value.searchQuery
        )
    }
    
    fun updateSearchQuery(query: String) {
        _uiState.value = _uiState.value.copy(searchQuery = query)
        
        if (query.isNotBlank()) {
            performSearch(query)
        } else {
            _uiState.value = _uiState.value.copy(searchResults = emptyList())
        }
    }
    
    private fun performSearch(query: String) {
        viewModelScope.launch {
            try {
                searchCourseUseCase(query).collect { result ->
                    result.fold(
                        onSuccess = { results ->
                            _uiState.value = _uiState.value.copy(searchResults = results)
                        },
                        onFailure = {
                            _uiState.value = _uiState.value.copy(searchResults = emptyList())
                        }
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(searchResults = emptyList())
            }
        }
    }
}

/**
 * 首页UI状态
 */
data class HomeUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val isLoggedIn: Boolean = false,
    val freeSeries: Course? = null,
    val paidSeries: List<Course> = emptyList(),
    val bundlePrice: Double = 0.0,
    val purchasedSeriesIds: Set<String> = emptySet(),
    val purchasedCategoryIds: Set<String> = emptySet(),
    val expandedSeriesIds: Set<String> = emptySet(),
    val expandedCategoryIds: Set<String> = emptySet(),
    val showSearch: Boolean = false,
    val searchQuery: String = "",
    val searchResults: List<Any> = emptyList() // TODO: 定义搜索结果类型
)
