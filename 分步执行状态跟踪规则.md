---
description: 
globs: 
alwaysApply: true
---
# 分步执行状态跟踪规则

## 📋 规则概述

所有修改规划、优化方案、实施计划都必须采用分步执行状态跟踪模式，确保任务可控、进度可视、质量可验。

## 📄 完整文档模板格式

### 文档标题格式
```markdown
# [项目名称][任务类型]规划文档

## 📋 规划概述

基于[问题分析/需求分析]，制定系统性[解决方案/实施方案]，确保[目标达成描述]。
```

### 进度总览表格格式
```markdown
## 📊 项目进度总览

| 步骤 | 任务名称 | 状态     | 开始时间 | 完成时间 |
| ---- | -------- | -------- | -------- | -------- |
| 1    | [任务1]  | ⏳ 待开始 | -        | -        |
| 2    | [任务2]  | ⏳ 待开始 | -        | -        |
| 3    | [任务3]  | ⏳ 待开始 | -        | -        |
| 4    | [任务4]  | ⏳ 待开始 | -        | -        |
| 5    | [任务5]  | ⏳ 待开始 | -        | -        |
| 6    | [任务6]  | ⏳ 待开始 | -        | -        |
| 7    | [任务7]  | ⏳ 待开始 | -        | -        |
| 8    | [任务8]  | ⏳ 待开始 | -        | -        |

### 🤖 Cursor AI 状态更新指令

**重要**：每完成一个步骤后，Cursor AI 必须执行以下操作：

1. **更新进度总览表格**：获取当前时间：get_current_time，将对应步骤的状态从 `⏳ 待开始` 改为 `✅ 已完成`，并填写开始时间和完成时间
2. **更新步骤详情状态**：修改具体步骤中的**状态**、**开始时间**、**完成时间**字段
3. **🔄 执行质量检查**：确保所有验收标准都已满足
4. **📋 生成完成报告**：汇报完成的具体内容和交付物
5. **继续执行下一步**：无需等待用户确认，直接继续执行下一步

**状态标记说明**：

- ⏳ 待开始：尚未开始执行
- 🔄 进行中：正在执行中
- ⏸️ 等待确认：已完成执行，等待用户确认
- ✅ 已完成：已成功完成并通过质量检查和用户确认
- ❌ 失败：执行失败，需要重新执行
- ⏸️ 暂停：暂停执行，等待条件满足
```

### 详细步骤格式
```markdown
## 🎯 详细[修复/实施]计划

### 步骤N：[任务名称]

**状态**：[⏳ 待开始 | 🔄 进行中 | ✅ 已完成 | ❌ 失败 | ⏸️ 暂停]  
**开始时间**：[YYYY-MM-DDTHH:mm:ss 或 -]  
**完成时间**：[YYYY-MM-DDTHH:mm:ss 或 -]  
**具体任务**：

1. [子任务1详细描述]
   - [具体操作1]
   - [具体操作2]
   - [具体操作3]

2. [子任务2详细描述]
   - [具体操作1]
   - [具体操作2]
   - [具体操作3]

3. [子任务3详细描述]
   - [具体操作1]
   - [具体操作2]
   - [具体操作3]

4. [子任务4详细描述]
   - [具体操作1]
   - [具体操作2]
   - [具体操作3]

**验收标准**：

- [ ] [验收项1具体描述]
- [ ] [验收项2具体描述]
- [ ] [验收项3具体描述]
- [ ] [验收项4具体描述]

---
```

### 执行原则格式
```markdown
## 🔄 执行原则

1. **顺序执行**：[执行顺序说明]
2. **状态同步**：每次状态变更必须更新时间戳
3. **验收驱动**：所有验收标准完成才能标记步骤完成
4. **问题记录**：[问题记录要求]
5. **进度汇报**：[进度汇报要求]
```

### 预期成果格式
```markdown
## 📈 预期成果

完成所有[修复/实施]步骤后，项目将达到：
- ✅ [成果1描述]
- ✅ [成果2描述]
- ✅ [成果3描述]
- ✅ [成果4描述]
- ✅ [成果5描述]
```

## 🔄 状态更新规则

### 开始执行时

1. **状态更新**：改为 🔄 进行中
2. **时间记录**：调用get_current_time获取开始时间
3. **表格同步**：同时更新进度总览表格中的状态和开始时间
4. **验收状态**：保持验收标准为未完成状态 [ ]

### 完成执行时

1. **状态更新**：改为 ✅ 已完成
2. **时间记录**：调用get_current_time获取完成时间
3. **表格同步**：同时更新进度总览表格中的状态和完成时间
4. **验收状态**：所有验收标准改为已完成 [x]
5. **完成报告**：生成详细的完成报告，包括交付物清单

### 执行失败时

1. **状态更新**：改为 ❌ 失败
2. **时间记录**：调用get_current_time获取失败时间
3. **表格同步**：同时更新进度总览表格
4. **失败分析**：在步骤后添加详细的失败原因说明
5. **解决方案**：提出重新执行或调整的建议

### 暂停执行时

1. **状态更新**：改为 ⏸️ 暂停
2. **暂停原因**：详细记录暂停原因
3. **恢复条件**：明确恢复执行的条件
4. **影响评估**：评估暂停对整体进度的影响

## 🎯 执行原则

1. **顺序执行**：严格按步骤编号依次执行，除非明确标注可并行
2. **状态同步**：每次状态变更必须同时更新时间戳和表格
3. **验收驱动**：所有验收标准完成才能标记步骤完成
4. **问题记录**：失败或暂停必须记录具体原因和解决方案
5. **进度可视**：通过状态emoji和表格快速识别整体进度
6. **质量保证**：每个步骤完成后必须进行质量检查
7. **文档更新**：实时更新文档状态，确保信息准确性

## 🔧 Cursor AI执行指令

### 执行任务时必须按以下顺序操作：

1. **获取当前时间**：调用get_current_time工具
2. **更新步骤状态**：将状态改为🔄进行中，填写开始时间
3. **更新进度表格**：同步更新进度总览表格中的对应行
4. **执行具体任务**：按照具体任务清单逐项完成
5. **检查验收标准**：逐项检查所有验收标准是否满足
6. **更新完成状态**：将状态改为✅已完成，填写完成时间
7. **生成完成报告**：汇报完成的具体内容和交付物
8. **等待用户确认**：等待用户明确回复才能执行下一步

### 文档创建指令

当需要创建新的规划文档时，必须：

1. **使用标准模板**：按照本规则的完整文档模板格式创建
2. **包含所有必要章节**：规划概述、进度总览、详细计划、执行原则、预期成果
3. **设置合理步骤数**：通常6-8个步骤，确保每个步骤任务量适中
4. **明确验收标准**：每个步骤至少4个具体的验收标准
5. **预估时间安排**：合理预估每个步骤的执行时间

### 文档更新指令

在执行过程中更新文档时，必须：

1. **保持格式一致**：严格按照模板格式更新
2. **同步更新多处**：同时更新进度表格和步骤详情
3. **记录时间戳**：所有时间必须使用get_current_time获取
4. **保留历史信息**：不删除已完成步骤的详细信息
5. **及时反映状态**：确保文档状态与实际执行状态一致

## 📝 适用场景

### 技术类规划
- 编译错误修复规划
- 代码重构实施计划
- 系统架构优化方案
- 性能优化执行计划
- 安全漏洞修复方案

### 项目类规划
- 功能开发计划
- 测试执行计划
- 部署上线流程
- 版本发布计划
- 维护升级方案

### 管理类规划
- 团队协作优化
- 流程改进计划
- 质量提升方案
- 文档完善计划
- 培训实施方案

## 🚀 最佳实践

### 步骤设计原则
1. **粒度适中**：每个步骤1-2小时完成
2. **逻辑清晰**：步骤间有明确的依赖关系
3. **可验证性**：每个步骤都有明确的验收标准
4. **可回滚性**：失败时能够回滚到上一个稳定状态

### 验收标准设计
1. **具体明确**：避免模糊的描述
2. **可测试性**：能够通过具体操作验证
3. **完整覆盖**：覆盖步骤的所有关键产出
4. **质量导向**：不仅关注完成度，更关注质量

### 状态管理技巧
1. **及时更新**：状态变化时立即更新文档
2. **详细记录**：记录状态变化的原因和背景
3. **问题跟踪**：失败时详细记录问题和解决方案
4. **进度可视**：通过表格和emoji快速了解进度

## 📊 质量检查清单

### 文档创建检查
- [ ] 标题格式正确
- [ ] 包含所有必要章节
- [ ] 进度表格格式正确
- [ ] 步骤数量合理（6-8个）
- [ ] 每个步骤有详细任务描述
- [ ] 每个步骤有明确验收标准
- [ ] 执行原则清晰明确
- [ ] 预期成果具体可达

### 执行过程检查
- [ ] 状态更新及时准确
- [ ] 时间戳记录完整
- [ ] 进度表格与步骤详情同步
- [ ] 验收标准逐项检查
- [ ] 完成报告详细具体
- [ ] 问题记录清晰完整
- [ ] 用户确认及时获取

### 文档质量检查
- [ ] 格式统一规范
- [ ] 内容完整准确
- [ ] 状态反映真实情况
- [ ] 时间记录准确
- [ ] 链接和引用正确
- [ ] 语言表达清晰
- [ ] 逻辑结构合理 