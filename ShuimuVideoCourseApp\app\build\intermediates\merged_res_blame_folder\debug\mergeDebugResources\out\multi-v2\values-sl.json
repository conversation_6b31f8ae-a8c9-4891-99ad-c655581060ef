{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-72:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\629a8056d9c025bf7ae40522f2b21f50\\transformed\\media3-session-1.4.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,249,327,422,514,610,692,783,883,965,1043,1146,1237,1331,1407,1502,1582,1685,1764,1835,1916,2001,2100", "endColumns": "78,114,77,94,91,95,81,90,99,81,77,102,90,93,75,94,79,102,78,70,80,84,98,101", "endOffsets": "129,244,322,417,509,605,687,778,878,960,1038,1141,1232,1326,1402,1497,1577,1680,1759,1830,1911,1996,2095,2197"}, "to": {"startLines": "60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,189,190,191,192,193,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4743,5005,5120,5198,5293,5385,5481,5563,5654,5754,5836,5914,6017,6108,6202,6278,6373,6453,16880,16959,17030,17111,17196,17295", "endColumns": "78,114,77,94,91,95,81,90,99,81,77,102,90,93,75,94,79,102,78,70,80,84,98,101", "endOffsets": "4817,5115,5193,5288,5380,5476,5558,5649,5749,5831,5909,6012,6103,6197,6273,6368,6448,6551,16954,17025,17106,17191,17290,17392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b26b2c6972d7ec14d495070bc19fa7d\\transformed\\media3-ui-1.4.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,958,1045,1133,1216,1314,1415,1498,1563,1660,1754,1825,1895,1959,2027,2149,2277,2399,2476,2556,2629,2709,2816,2924,2992,3057,3110,3168,3216,3277,3347,3416,3479,3544,3607,3664,3740,3792,3855,3932,4009,4063", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,51,62,76,76,53,65", "endOffsets": "318,640,953,1040,1128,1211,1309,1410,1493,1558,1655,1749,1820,1890,1954,2022,2144,2272,2394,2471,2551,2624,2704,2811,2919,2987,3052,3105,3163,3211,3272,3342,3411,3474,3539,3602,3659,3735,3787,3850,3927,4004,4058,4124"}, "to": {"startLines": "2,11,17,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,373,695,6556,6643,6731,6814,6912,7013,7096,7161,7258,7352,7423,7493,7557,7625,7747,7875,7997,8074,8154,8227,8307,8414,8522,8590,9331,9384,9442,9490,9551,9621,9690,9753,9818,9881,9938,10014,10066,10129,10206,10283,10337", "endLines": "10,16,22,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,51,62,76,76,53,65", "endOffsets": "368,690,1003,6638,6726,6809,6907,7008,7091,7156,7253,7347,7418,7488,7552,7620,7742,7870,7992,8069,8149,8222,8302,8409,8517,8585,8650,9379,9437,9485,9546,9616,9685,9748,9813,9876,9933,10009,10061,10124,10201,10278,10332,10398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d9611cabb0369607a576bde775e2495\\transformed\\foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,90", "endOffsets": "140,231"}, "to": {"startLines": "208,209", "startColumns": "4,4", "startOffsets": "18500,18590", "endColumns": "89,90", "endOffsets": "18585,18676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2936cbfd3caa73223b489c015e1e2446\\transformed\\media3-exoplayer-1.4.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,183,247,311,386,467,566,657", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "118,178,242,306,381,462,561,652,726"}, "to": {"startLines": "104,105,106,107,108,109,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8655,8723,8783,8847,8911,8986,9067,9166,9257", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "8718,8778,8842,8906,8981,9062,9161,9252,9326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\93a8666d95f1b2e5574541723f67c731\\transformed\\core-1.15.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "50,51,52,53,54,55,56,204", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3748,3845,3947,4045,4149,4252,4354,18130", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3840,3942,4040,4144,4247,4349,4466,18226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\806eeec989dab5f6c89001161fc1ea32\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1008,1120,1222,1330,1417,1520,1639,1720,1798,1890,1984,2079,2173,2268,2362,2458,2558,2650,2742,2826,2934,3042,3142,3255,3363,3468,3648,17739", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "1115,1217,1325,1412,1515,1634,1715,1793,1885,1979,2074,2168,2263,2357,2453,2553,2645,2737,2821,2929,3037,3137,3250,3358,3463,3643,3743,17818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d8a412d674739c339798b6565d8f6383\\transformed\\ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,980,1066,1138,1215,1295,1373,1451,1521", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,975,1061,1133,1210,1290,1368,1446,1516,1637"}, "to": {"startLines": "57,58,59,61,62,130,131,195,196,197,198,200,201,202,203,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4471,4566,4647,4822,4920,10403,10480,17397,17489,17571,17653,17823,17895,17972,18052,18231,18309,18379", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "4561,4642,4738,4915,5000,10475,10562,17484,17566,17648,17734,17890,17967,18047,18125,18304,18374,18495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e763ae93d7b51c12c0058b9fd0f99eb\\transformed\\material3-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,313,431,560,670,766,879,1019,1145,1288,1373,1472,1565,1662,1779,1901,2005,2142,2276,2407,2591,2718,2841,2966,3088,3182,3280,3400,3524,3624,3733,3839,3982,4129,4238,4340,4424,4519,4615,4723,4811,4897,5000,5082,5165,5260,5360,5451,5548,5636,5740,5837,5939,6081,6163,6269", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "179,308,426,555,665,761,874,1014,1140,1283,1368,1467,1560,1657,1774,1896,2000,2137,2271,2402,2586,2713,2836,2961,3083,3177,3275,3395,3519,3619,3728,3834,3977,4124,4233,4335,4419,4514,4610,4718,4806,4892,4995,5077,5160,5255,5355,5446,5543,5631,5735,5832,5934,6076,6158,6264,6363"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10567,10696,10825,10943,11072,11182,11278,11391,11531,11657,11800,11885,11984,12077,12174,12291,12413,12517,12654,12788,12919,13103,13230,13353,13478,13600,13694,13792,13912,14036,14136,14245,14351,14494,14641,14750,14852,14936,15031,15127,15235,15323,15409,15512,15594,15677,15772,15872,15963,16060,16148,16252,16349,16451,16593,16675,16781", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "10691,10820,10938,11067,11177,11273,11386,11526,11652,11795,11880,11979,12072,12169,12286,12408,12512,12649,12783,12914,13098,13225,13348,13473,13595,13689,13787,13907,14031,14131,14240,14346,14489,14636,14745,14847,14931,15026,15122,15230,15318,15404,15507,15589,15672,15767,15867,15958,16055,16143,16247,16344,16446,16588,16670,16776,16875"}}]}]}