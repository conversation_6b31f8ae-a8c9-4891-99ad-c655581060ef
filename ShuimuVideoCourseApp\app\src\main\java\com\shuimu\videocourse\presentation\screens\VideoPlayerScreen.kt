package com.shuimu.videocourse.presentation.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.videocourse.presentation.components.components_full.video.VideoPlayer
import com.shuimu.videocourse.presentation.components.components_full.video.VideoControls
import com.shuimu.videocourse.presentation.components.components_full.video.VideoInfoPanel
import com.shuimu.videocourse.presentation.components.components_full.status.LoadingIndicator
import com.shuimu.videocourse.presentation.components.components_full.status.ErrorMessage
import com.shuimu.videocourse.presentation.viewmodel.VideoPlayerViewModel
import com.shuimu.videocourse.presentation.theme.ShuimuVideoCourseTheme

/**
 * 视频播放页 - 对应02-视频播放页.html
 * 
 * 功能特性：
 * - AndroidX Media3 1.6.1视频播放器
 * - 强制缓存+离线播放
 * - 播放控制（播放/暂停、进度、倍速、全屏）
 * - 断点续播
 * - 防录屏水印
 * - 学习进度记录
 */
@Composable
fun VideoPlayerScreen(
    videoId: String,
    onNavigateBack: () -> Unit = {},
    viewModel: VideoPlayerViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(videoId) {
        viewModel.loadVideo(videoId)
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    LoadingIndicator(message = "准备视频中...")
                }
            }
            
            uiState.error != null -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    ErrorMessage(
                        message = uiState.error!!,
                        onRetry = { viewModel.loadVideo(videoId) }
                    )
                }
            }
            
            uiState.video != null -> {
                // 视频播放器区域
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(16f / 9f)
                ) {
                    VideoPlayer(
                        videoUrl = uiState.video!!.localPath ?: uiState.video!!.streamUrl,
                        isFullscreen = uiState.isFullscreen,
                        currentPosition = uiState.currentPosition,
                        duration = uiState.duration,
                        isPlaying = uiState.isPlaying,
                        playbackSpeed = uiState.playbackSpeed,
                        onPositionChange = { viewModel.seekTo(it) },
                        onPlayPause = { viewModel.togglePlayPause() },
                        onFullscreenToggle = { viewModel.toggleFullscreen() },
                        onSpeedChange = { viewModel.setPlaybackSpeed(it) }
                    )
                    
                    // 防录屏水印
                    if (uiState.showWatermark) {
                        WatermarkOverlay(
                            text = uiState.watermarkText,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }
                
                // 非全屏时显示视频信息和控制面板
                if (!uiState.isFullscreen) {
                    // 视频控制栏
                    VideoControls(
                        isPlaying = uiState.isPlaying,
                        currentPosition = uiState.currentPosition,
                        duration = uiState.duration,
                        playbackSpeed = uiState.playbackSpeed,
                        onPlayPause = { viewModel.togglePlayPause() },
                        onSeek = { viewModel.seekTo(it) },
                        onSpeedChange = { viewModel.setPlaybackSpeed(it) },
                        onFullscreen = { viewModel.toggleFullscreen() }
                    )
                    
                    // 视频信息面板
                    VideoInfoPanel(
                        video = uiState.video!!,
                        learningProgress = uiState.learningProgress,
                        relatedVideos = uiState.relatedVideos,
                        onRelatedVideoClick = { relatedVideoId ->
                            viewModel.loadVideo(relatedVideoId)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(16.dp)
                    )
                }
            }
        }
    }
    
    // 处理返回键
    BackHandler(enabled = uiState.isFullscreen) {
        if (uiState.isFullscreen) {
            viewModel.toggleFullscreen()
        } else {
            onNavigateBack()
        }
    }
    
    // 自动保存播放进度
    LaunchedEffect(uiState.currentPosition) {
        if (uiState.currentPosition > 0) {
            viewModel.saveProgress()
        }
    }
}

@Composable
private fun WatermarkOverlay(
    text: String,
    modifier: Modifier = Modifier
) {
    // TODO: 实现动态水印显示
    // 防录屏水印，动态位置变化
}

@Preview(showBackground = true)
@Composable
fun VideoPlayerScreenPreview() {
    ShuimuVideoCourseTheme {
        VideoPlayerScreen(videoId = "sample_video_id")
    }
}
