package com.shuimu.videocourse.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.shuimu.videocourse.domain.usecase.user.GetUserInfoUseCase
import com.shuimu.videocourse.domain.usecase.user.LogoutUseCase
import com.shuimu.videocourse.domain.model.User
import javax.inject.Inject

/**
 * 我的页面ViewModel
 * 
 * 负责管理：
 * - 用户信息展示
 * - 学习统计数据
 * - 分享收益数据
 * - 用户操作（编辑资料、退出登录）
 */
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val getUserInfoUseCase: GetUserInfoUseCase,
    private val logoutUseCase: LogoutUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ProfileUiState())
    val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()
    
    fun loadUserProfile() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                getUserInfoUseCase().collect { result ->
                    result.fold(
                        onSuccess = { user ->
                            if (user != null) {
                                _uiState.value = _uiState.value.copy(
                                    isLoading = false,
                                    isLoggedIn = true,
                                    user = user,
                                    purchasedCoursesCount = user.purchasedCoursesCount,
                                    totalLearningTime = user.totalLearningTime,
                                    completionRate = user.completionRate,
                                    shareCount = user.shareCount,
                                    conversionCount = user.conversionCount,
                                    totalRevenue = user.totalRevenue
                                )
                            } else {
                                _uiState.value = _uiState.value.copy(
                                    isLoading = false,
                                    isLoggedIn = false
                                )
                            }
                        },
                        onFailure = {
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                isLoggedIn = false
                            )
                        }
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isLoggedIn = false
                )
            }
        }
    }
    
    fun editProfile() {
        // TODO: 实现编辑资料功能
    }
    
    fun logout() {
        viewModelScope.launch {
            try {
                logoutUseCase()
                _uiState.value = ProfileUiState() // 重置状态
            } catch (e: Exception) {
                // 处理退出登录失败
            }
        }
    }
}

/**
 * 我的页面UI状态
 */
data class ProfileUiState(
    val isLoading: Boolean = false,
    val isLoggedIn: Boolean = false,
    val user: User? = null,
    val purchasedCoursesCount: Int = 0,
    val totalLearningTime: Long = 0L, // 总学习时长（分钟）
    val completionRate: Float = 0f, // 完成率（0-1）
    val shareCount: Int = 0, // 分享次数
    val conversionCount: Int = 0, // 转化人数
    val totalRevenue: Double = 0.0 // 累计收益
)
