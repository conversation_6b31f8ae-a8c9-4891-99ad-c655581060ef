package com.shuimu.videocourse.presentation.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.videocourse.presentation.components.components_full.basic.ShuimuTextField
import com.shuimu.videocourse.presentation.components.components_full.basic.ShuimuButton
import com.shuimu.videocourse.presentation.components.components_full.basic.ShuimuCard
import com.shuimu.videocourse.presentation.components.components_full.status.LoadingIndicator
import com.shuimu.videocourse.presentation.viewmodel.LoginViewModel
import com.shuimu.videocourse.presentation.theme.ShuimuVideoCourseTheme

/**
 * 登录页面 - 对应05-登录页面.html
 * 
 * 功能特性：
 * - 用户名+密码登录
 * - 登录状态管理
 * - 单设备在线控制
 * - 表单验证
 */
@Composable
fun LoginScreen(
    onLoginSuccess: () -> Unit = {},
    onNavigateBack: () -> Unit = {},
    viewModel: LoginViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        ShuimuCard(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "登录水幕视频课程",
                    style = MaterialTheme.typography.headlineMedium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 用户名输入框
                ShuimuTextField(
                    value = uiState.username,
                    onValueChange = { viewModel.updateUsername(it) },
                    label = "用户名",
                    isError = uiState.usernameError != null,
                    errorMessage = uiState.usernameError,
                    enabled = !uiState.isLoading,
                    modifier = Modifier.fillMaxWidth()
                )
                
                // 密码输入框
                ShuimuTextField(
                    value = uiState.password,
                    onValueChange = { viewModel.updatePassword(it) },
                    label = "密码",
                    isError = uiState.passwordError != null,
                    errorMessage = uiState.passwordError,
                    visualTransformation = PasswordVisualTransformation(),
                    enabled = !uiState.isLoading,
                    modifier = Modifier.fillMaxWidth()
                )
                
                // 错误信息显示
                if (uiState.loginError != null) {
                    Text(
                        text = uiState.loginError!!,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 登录按钮
                ShuimuButton(
                    text = if (uiState.isLoading) "登录中..." else "登录",
                    onClick = { viewModel.login() },
                    enabled = !uiState.isLoading && uiState.canLogin,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    if (uiState.isLoading) {
                        LoadingIndicator(
                            size = 16.dp,
                            modifier = Modifier.padding(end = 8.dp)
                        )
                    }
                }
                
                // 返回按钮
                TextButton(
                    onClick = onNavigateBack,
                    enabled = !uiState.isLoading
                ) {
                    Text("返回")
                }
            }
        }
    }
    
    // 处理登录成功
    LaunchedEffect(uiState.isLoginSuccess) {
        if (uiState.isLoginSuccess) {
            onLoginSuccess()
        }
    }
}

@Preview(showBackground = true)
@Composable
fun LoginScreenPreview() {
    ShuimuVideoCourseTheme {
        LoginScreen()
    }
}
