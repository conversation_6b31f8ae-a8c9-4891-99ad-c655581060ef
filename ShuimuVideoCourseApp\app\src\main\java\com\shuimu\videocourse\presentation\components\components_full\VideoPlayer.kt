package com.shuimu.videocourse.presentation.components

import android.content.Context
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Fullscreen
import androidx.compose.material.icons.filled.FullscreenExit
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.ui.PlayerView
import kotlinx.coroutines.delay

/**
 * 视频播放器组件 - 23个核心组件之一
 * 
 * 功能特性：
 * 1. 强制缓存架构：所有视频必须先缓存到本地才能播放
 * 2. 离线播放支持：支持完全离线播放
 * 3. 断点续播：自动记录和恢复播放进度
 * 4. 防录屏水印：动态水印显示
 * 5. 播放控制：播放/暂停、进度控制、倍速播放、全屏切换
 * 
 * @param videoUrl 视频URL（网盘直链或本地文件路径）
 * @param isLocalFile 是否为本地缓存文件
 * @param onProgressUpdate 播放进度更新回调
 * @param onPlaybackComplete 播放完成回调
 * @param watermarkText 防录屏水印文本
 * @param modifier 修饰符
 */
@Composable
fun VideoPlayer(
    videoUrl: String,
    isLocalFile: Boolean = false,
    onProgressUpdate: (Long, Long) -> Unit = { _, _ -> },
    onPlaybackComplete: () -> Unit = {},
    watermarkText: String = "",
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var isPlaying by remember { mutableStateOf(false) }
    var isFullscreen by remember { mutableStateOf(false) }
    var currentPosition by remember { mutableLongStateOf(0L) }
    var duration by remember { mutableLongStateOf(0L) }
    var showControls by remember { mutableStateOf(true) }
    var playbackSpeed by remember { mutableFloatStateOf(1.0f) }
    
    // ExoPlayer实例
    val exoPlayer = remember {
        createExoPlayer(context, videoUrl, isLocalFile)
    }
    
    // 播放状态监听
    LaunchedEffect(exoPlayer) {
        val listener = object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_ENDED -> {
                        onPlaybackComplete()
                    }
                }
            }
            
            override fun onIsPlayingChanged(playing: Boolean) {
                isPlaying = playing
            }
        }
        exoPlayer.addListener(listener)
    }
    
    // 进度更新
    LaunchedEffect(exoPlayer) {
        while (true) {
            if (exoPlayer.isPlaying) {
                currentPosition = exoPlayer.currentPosition
                duration = exoPlayer.duration.takeIf { it > 0 } ?: 0L
                onProgressUpdate(currentPosition, duration)
            }
            delay(1000)
        }
    }
    
    // 控制栏自动隐藏
    LaunchedEffect(showControls) {
        if (showControls && isPlaying) {
            delay(3000)
            showControls = false
        }
    }
    
    // 释放资源
    DisposableEffect(exoPlayer) {
        onDispose {
            exoPlayer.release()
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(16f / 9f)
            .clip(RoundedCornerShape(8.dp))
            .background(Color.Black)
    ) {
        // 视频播放视图
        AndroidView(
            factory = { ctx ->
                PlayerView(ctx).apply {
                    player = exoPlayer
                    useController = false // 使用自定义控制栏
                    setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                }
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // 防录屏水印
        if (watermarkText.isNotEmpty()) {
            WatermarkOverlay(
                text = watermarkText,
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // 播放控制栏
        if (showControls) {
            VideoControls(
                isPlaying = isPlaying,
                isFullscreen = isFullscreen,
                currentPosition = currentPosition,
                duration = duration,
                playbackSpeed = playbackSpeed,
                onPlayPause = {
                    if (isPlaying) {
                        exoPlayer.pause()
                    } else {
                        exoPlayer.play()
                    }
                },
                onSeek = { position ->
                    exoPlayer.seekTo(position)
                },
                onFullscreenToggle = {
                    isFullscreen = !isFullscreen
                },
                onSpeedChange = { speed ->
                    playbackSpeed = speed
                    exoPlayer.setPlaybackSpeed(speed)
                },
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
        
        // 点击显示/隐藏控制栏
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable {
                    showControls = !showControls
                }
        )
    }
}

/**
 * 创建ExoPlayer实例
 * 支持强制缓存和离线播放
 */
private fun createExoPlayer(
    context: Context,
    videoUrl: String,
    isLocalFile: Boolean
): ExoPlayer {
    val exoPlayer = ExoPlayer.Builder(context).build()
    
    val mediaItem = if (isLocalFile) {
        // 本地缓存文件播放
        MediaItem.fromUri(Uri.parse("file://$videoUrl"))
    } else {
        // 网络视频（需要先缓存）
        MediaItem.fromUri(videoUrl)
    }
    
    // 配置数据源工厂（支持缓存）
    val dataSourceFactory = if (isLocalFile) {
        DefaultDataSource.Factory(context)
    } else {
        // 网络数据源（应该先通过缓存管理器下载）
        CacheDataSource.Factory()
            .setCache(VideoCacheManager.getCache(context))
            .setUpstreamDataSourceFactory(DefaultDataSource.Factory(context))
    }
    
    val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
        .createMediaSource(mediaItem)
    
    exoPlayer.setMediaSource(mediaSource)
    exoPlayer.prepare()
    
    return exoPlayer
}

/**
 * 防录屏水印覆盖层
 */
@Composable
private fun WatermarkOverlay(
    text: String,
    modifier: Modifier = Modifier
) {
    var offsetX by remember { mutableFloatStateOf(0f) }
    var offsetY by remember { mutableFloatStateOf(0f) }
    
    // 动态水印位置
    LaunchedEffect(Unit) {
        while (true) {
            offsetX = (0..100).random().toFloat()
            offsetY = (0..100).random().toFloat()
            delay(5000) // 每5秒移动一次
        }
    }
    
    Box(modifier = modifier) {
        Text(
            text = text,
            color = Color.White.copy(alpha = 0.3f),
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier
                .offset(x = offsetX.dp, y = offsetY.dp)
                .padding(16.dp)
        )
    }
}

/**
 * 视频缓存管理器
 * 处理强制缓存逻辑
 */
object VideoCacheManager {
    private var cache: androidx.media3.datasource.cache.Cache? = null
    
    fun getCache(context: Context): androidx.media3.datasource.cache.Cache {
        if (cache == null) {
            val cacheDir = context.cacheDir.resolve("video_cache")
            cache = androidx.media3.datasource.cache.SimpleCache(
                cacheDir,
                androidx.media3.datasource.cache.NoOpCacheEvictor(),
                androidx.media3.database.StandaloneDatabaseProvider(context)
            )
        }
        return cache!!
    }
    
    fun clearCache() {
        cache?.release()
        cache = null
    }
} 