package com.shuimu.videocourse.presentation.components.video

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.PlaylistPlay
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.display.VideoItem
import com.shuimu.videocourse.presentation.components.state.CacheStatus
import com.shuimu.videocourse.presentation.theme.ShuimuTheme
import kotlinx.coroutines.launch

/**
 * 播放列表项数据类
 */
data class PlaylistItem(
    val id: String,
    val title: String,
    val duration: String,
    val watchProgress: Float = 0f, // 0-100
    val watchCount: Int = 0,
    val isCurrentPlaying: Boolean = false,
    val isPurchased: Boolean = true,
    val isFree: Boolean = false,
    val cacheStatus: String? = null,
    val category: String? = null
)

/**
 * 播放列表数据类
 */
data class PlaylistData(
    val title: String,
    val totalVideos: Int,
    val totalDuration: String,
    val completedVideos: Int,
    val items: List<PlaylistItem>
)

/**
 * 播放列表视频项数据类
 */
data class PlaylistVideoItem(
    val id: String,
    val title: String,
    val duration: Int,
    val progress: Float = 0f,
    val isPurchased: Boolean = true,
    val cacheStatus: CacheStatus = CacheStatus.NotCached,
    val thumbnailUrl: String? = null
)

/**
 * 播放列表组件
 * 
 * 基于原型：UI Prototype/02-视频播放页.html 第2140行 fullscreenPlaylistPanel
 * 
 * 功能特性：
 * - 播放列表容器和视频项列表
 * - 全屏模式支持和滚动导航功能
 * - 复用VideoItem组件
 * - 自动滚动到当前播放视频
 * - 支持展开/收起模式
 * 
 * @param title 播放列表标题
 * @param videos 视频列表
 * @param currentVideoIndex 当前播放视频索引
 * @param isFullscreen 是否全屏模式
 * @param isExpanded 是否展开状态
 * @param modifier 修饰符
 * @param onVideoClick 视频点击回调
 * @param onToggleExpand 展开/收起回调
 * @param onClose 关闭回调
 */
@Composable
fun PlaylistPanel(
    title: String,
    videos: List<PlaylistVideoItem>,
    currentVideoIndex: Int = 0,
    isFullscreen: Boolean = false,
    isExpanded: Boolean = false,
    modifier: Modifier = Modifier,
    onVideoClick: (Int) -> Unit = {},
    onToggleExpand: () -> Unit = {},
    onClose: () -> Unit = {}
) {
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    
    // 自动滚动到当前播放视频
    LaunchedEffect(currentVideoIndex) {
        if (currentVideoIndex >= 0 && currentVideoIndex < videos.size) {
            coroutineScope.launch {
                listState.animateScrollToItem(currentVideoIndex)
            }
        }
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .let { mod ->
                if (isFullscreen) {
                    mod.fillMaxHeight(0.6f)
                } else if (isExpanded) {
                    mod.fillMaxHeight(0.4f)
                } else {
                    mod.height(200.dp)
                }
            },
        shape = RoundedCornerShape(
            topStart = 16.dp,
            topEnd = 16.dp,
            bottomStart = if (isFullscreen) 0.dp else 16.dp,
            bottomEnd = if (isFullscreen) 0.dp else 16.dp
        ),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 头部标题栏
            PlaylistHeader(
                title = title,
                videoCount = videos.size,
                currentIndex = currentVideoIndex,
                isFullscreen = isFullscreen,
                isExpanded = isExpanded,
                onToggleExpand = onToggleExpand,
                onClose = onClose
            )
            
            Divider(
                color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f),
                thickness = 0.5.dp
            )
            
            // 视频列表
            LazyColumn(
                state = listState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(vertical = 8.dp)
            ) {
                itemsIndexed(videos) { index, video ->
                    PlaylistVideoItemComponent(
                        video = video,
                        isCurrentPlaying = index == currentVideoIndex,
                        index = index + 1,
                        onClick = { onVideoClick(index) }
                    )
                    
                    if (index < videos.size - 1) {
                        Divider(
                            modifier = Modifier.padding(start = 60.dp),
                            color = MaterialTheme.colorScheme.outline.copy(alpha = 0.1f),
                            thickness = 0.5.dp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 播放列表头部组件
 */
@Composable
private fun PlaylistHeader(
    title: String,
    videoCount: Int,
    currentIndex: Int,
    isFullscreen: Boolean,
    isExpanded: Boolean,
    onToggleExpand: () -> Unit,
    onClose: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧：标题和统计
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                ),
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            Text(
                text = "${currentIndex + 1}/$videoCount",
                style = MaterialTheme.typography.bodySmall.copy(
                    fontSize = 12.sp
                ),
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
        
        // 右侧：操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 展开/收起按钮
            if (!isFullscreen) {
                IconButton(
                    onClick = onToggleExpand,
                    modifier = Modifier.size(32.dp)
                ) {
                    Text(
                        text = if (isExpanded) "🔽" else "🔼",
                        fontSize = 14.sp
                    )
                }
            }
            
            // 关闭按钮
            IconButton(
                onClick = onClose,
                modifier = Modifier.size(32.dp)
            ) {
                Text(
                    text = "✕",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
    }
}

/**
 * 播放列表视频项组件
 */
@Composable
private fun PlaylistVideoItemComponent(
    video: PlaylistVideoItem,
    isCurrentPlaying: Boolean,
    index: Int,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(
                color = if (isCurrentPlaying) {
                    Color(0xFF667eea).copy(alpha = 0.1f)
                } else {
                    Color.Transparent
                }
            )
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 序号或播放状态
        Box(
            modifier = Modifier
                .size(32.dp)
                .background(
                    color = if (isCurrentPlaying) {
                        Color(0xFF667eea)
                    } else {
                        MaterialTheme.colorScheme.outline.copy(alpha = 0.2f)
                    },
                    shape = RoundedCornerShape(6.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            if (isCurrentPlaying) {
                Text(
                    text = "▶",
                    fontSize = 12.sp,
                    color = Color.White,
                    fontWeight = FontWeight.Bold
                )
            } else {
                Text(
                    text = index.toString(),
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    fontWeight = FontWeight.Medium
                )
            }
        }
        
        // 视频信息
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = video.title,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = if (isCurrentPlaying) FontWeight.Bold else FontWeight.Normal,
                    fontSize = 14.sp
                ),
                color = if (isCurrentPlaying) {
                    Color(0xFF667eea)
                } else {
                    MaterialTheme.colorScheme.onSurface
                },
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = formatDuration(video.duration),
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontSize = 11.sp
                    ),
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
                
                if (video.progress > 0) {
                    Text(
                        text = "已看${(video.progress * 100).toInt()}%",
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontSize = 11.sp
                        ),
                        color = Color(0xFF10b981)
                    )
                }
                
                if (!video.isPurchased) {
                    Text(
                        text = "🔒",
                        fontSize = 11.sp
                    )
                }
            }
        }
        
        // 缓存状态
        if (video.isPurchased) {
            Box(
                modifier = Modifier
                    .size(16.dp)
                    .background(
                        color = when (video.cacheStatus) {
                            CacheStatus.Cached -> Color(0xFF10b981)
                            CacheStatus.Downloading -> Color(0xFF3b82f6)
                            CacheStatus.Error -> Color(0xFFef4444)
                            else -> Color.Transparent
                        },
                        shape = RoundedCornerShape(2.dp)
                    )
            ) {
                if (video.cacheStatus != CacheStatus.NotCached) {
                    Text(
                        text = when (video.cacheStatus) {
                            CacheStatus.Cached -> "✓"
                            CacheStatus.Downloading -> "⬇"
                            CacheStatus.Error -> "✗"
                            else -> ""
                        },
                        fontSize = 8.sp,
                        color = Color.White,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }
    }
}

/**
 * 格式化时长显示
 */
private fun formatDuration(seconds: Int): String {
    val minutes = seconds / 60
    val secs = seconds % 60
    return String.format("%d:%02d", minutes, secs)
}

/**
 * 预览组件
 */
@Composable
fun PlaylistPanelPreview() {
    ShuimuTheme {
        val sampleVideos = listOf(
            PlaylistVideoItem(
                id = "1",
                title = "Kotlin基础语法入门",
                duration = 1200,
                progress = 1f,
                isPurchased = true,
                cacheStatus = CacheStatus.Cached
            ),
            PlaylistVideoItem(
                id = "2",
                title = "面向对象编程概念详解",
                duration = 1800,
                progress = 0.6f,
                isPurchased = true,
                cacheStatus = CacheStatus.Downloading
            ),
            PlaylistVideoItem(
                id = "3",
                title = "协程与异步编程实战",
                duration = 2400,
                progress = 0f,
                isPurchased = true,
                cacheStatus = CacheStatus.NotCached
            ),
            PlaylistVideoItem(
                id = "4",
                title = "高级特性与最佳实践",
                duration = 3000,
                progress = 0f,
                isPurchased = false,
                cacheStatus = CacheStatus.NotCached
            )
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 普通模式
            PlaylistPanel(
                title = "Kotlin从入门到精通",
                videos = sampleVideos,
                currentVideoIndex = 1,
                isExpanded = false
            )
            
            // 展开模式
            PlaylistPanel(
                title = "Android开发实战课程",
                videos = sampleVideos,
                currentVideoIndex = 0,
                isExpanded = true
            )
        }
    }
} 