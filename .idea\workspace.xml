<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a23e3fba-c9d5-4570-a94d-427fc5904d15" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/gradle.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/checksums/checksums.lock" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/checksums/checksums.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/executionHistory/executionHistory.bin" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/executionHistory/executionHistory.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/executionHistory/executionHistory.lock" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/executionHistory/executionHistory.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/fileHashes/fileHashes.bin" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/fileHashes/fileHashes.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/fileHashes/fileHashes.lock" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/fileHashes/fileHashes.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/fileHashes/resourceHashesCache.bin" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/8.13/fileHashes/resourceHashesCache.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/buildOutputCleanup/buildOutputCleanup.lock" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/buildOutputCleanup/buildOutputCleanup.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/buildOutputCleanup/outputFiles.bin" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/buildOutputCleanup/outputFiles.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/file-system.probe" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.gradle/file-system.probe" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/.idea/gradle.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/.idea/gradle.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/VideoDatabase_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/AppSettingsDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/CategoryDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/CommissionDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/DownloadTaskDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/LearningProgressDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/PaymentDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/PurchaseDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/SeriesDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/ShareRecordDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/UserDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/UserProfileDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/VideoCacheDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/VideoDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/data/local/database/dao/WatchHistoryDao_Impl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideAppSettingsDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideCategoryDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideCommissionDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideDownloadTaskDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideLearningProgressDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvidePaymentDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvidePurchaseDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideSeriesDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideShareRecordDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideUserDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideUserProfileDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideVideoCacheDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideVideoDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideVideoDatabaseFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/DatabaseModule_ProvideWatchHistoryDaoFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/NetworkModule_ProvideGsonFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/NetworkModule_ProvideHttpLoggingInterceptorFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/NetworkModule_ProvideOkHttpClientFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/di/NetworkModule_ProvideRetrofitFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/auth/GetUserInfoUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/auth/LoginUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/auth/LogoutUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/course/GetCourseListUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/course/GetVideoDetailUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/course/SearchCourseUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/flow/NetworkMonitor_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/flow/PaymentFlowCoordinator_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/flow/VideoPlayFlowCoordinator_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/payment/CalculatePriceUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/payment/CreateOrderUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/payment/ProcessPaymentUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/purchase/CreateOrderUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/purchase/VerifyPurchaseUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/user/GetLearningStatsUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/user/LoginUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/video/CacheVideoUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/video/ManageCacheUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/video/PlayVideoUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/domain/usecase/video/UpdateProgressUseCase_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/LoginViewModel_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/LoginViewModel_HiltModules.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/LoginViewModel_HiltModules_KeyModule_ProvideFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/PaymentViewModel_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/PaymentViewModel_HiltModules.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/PaymentViewModel_HiltModules_KeyModule_ProvideFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/ProfileViewModel_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/ProfileViewModel_HiltModules.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/ProfileViewModel_HiltModules_KeyModule_ProvideFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/PurchaseHistoryViewModel_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/PurchaseHistoryViewModel_HiltModules.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/PurchaseHistoryViewModel_HiltModules_KeyModule_ProvideFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/SettingsViewModel_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/SettingsViewModel_HiltModules.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/SettingsViewModel_HiltModules_KeyModule_ProvideFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/ShareRevenueViewModel_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/ShareRevenueViewModel_HiltModules.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/ShareRevenueViewModel_HiltModules_KeyModule_ProvideFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/VideoPlayerViewModel_Factory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/VideoPlayerViewModel_HiltModules.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/com/shuimu/videocourse/presentation/viewmodel/VideoPlayerViewModel_HiltModules_KeyModule_ProvideFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_di_DatabaseModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_di_NetworkModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_LoginViewModel_HiltModules_BindsModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_LoginViewModel_HiltModules_KeyModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_PaymentViewModel_HiltModules_BindsModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_PaymentViewModel_HiltModules_KeyModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_ProfileViewModel_HiltModules_BindsModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_ProfileViewModel_HiltModules_KeyModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_PurchaseHistoryViewModel_HiltModules_BindsModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_PurchaseHistoryViewModel_HiltModules_KeyModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_SettingsViewModel_HiltModules_BindsModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_SettingsViewModel_HiltModules_KeyModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_ShareRevenueViewModel_HiltModules_BindsModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_ShareRevenueViewModel_HiltModules_KeyModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_VideoPlayerViewModel_HiltModules_BindsModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/generated/ksp/debug/java/hilt_aggregated_deps/_com_shuimu_videocourse_presentation_viewmodel_VideoPlayerViewModel_HiltModules_KeyModule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/linked_resources_binary_format/debug/processDebugResources/linked-resources-binary-format-debug.ap_" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/linked_resources_binary_format/debug/processDebugResources/linked-resources-binary-format-debug.ap_" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/manifest_merge_blame_file/debug/processDebugMainManifest/manifest-merger-blame-debug-report.txt" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/manifest_merge_blame_file/debug/processDebugMainManifest/manifest-merger-blame-debug-report.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/merged_manifest/debug/processDebugMainManifest/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/merged_manifest/debug/processDebugMainManifest/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/merged_manifests/debug/processDebugManifest/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/merged_manifests/debug/processDebugManifest/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/packaged_manifests/debug/processDebugManifestForPackage/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/intermediates/packaged_manifests/debug/processDebugManifestForPackage/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/kotlin/compileDebugKotlin/local-state/build-history.bin" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/kotlin/compileDebugKotlin/local-state/build-history.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/outputs/logs/manifest-merger-debug-report.txt" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/build/outputs/logs/manifest-merger-debug-report.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/domain/usecase/flow/DataSyncCoordinator.kt" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/domain/usecase/flow/DataSyncCoordinator.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/domain/usecase/flow/UserFlowCoordinator.kt" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/domain/usecase/flow/UserFlowCoordinator.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/ComponentRegistry.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/VideoControls.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/VideoPlayer.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/basic/BadgeComponent.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/basic/BadgeTestActivity.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/basic/ProgressBar.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/basic/ShuimuButton.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/basic/ShuimuCard.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/basic/ShuimuTextField.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/display/CategoryCard.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/display/SearchItem.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/display/SeriesHeader.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/display/VideoItem.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/modal/PaymentModal.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/modal/PurchaseModal.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/modal/SearchModal.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/modal/ShareModal.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/navigation/BottomNavigationBar.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/navigation/TopAppBar.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/state/CacheStatusIndicator.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/state/EmptyState.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/state/ErrorMessage.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/state/LoadingIndicator.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/status/BadgeComponent.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/status/CacheStatusIndicator.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/status/EmptyState.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/status/ErrorMessage.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/status/LoadingIndicator.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/user/MenuItem.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/user/SettingItem.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/user/UserAvatar.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/user/UserProfileHeader.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/user/UserStats.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/video/PlaylistPanel.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/video/VideoControls.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/video/VideoInfoPanel.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/components/video/VideoPlayer.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/AboutUsScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/CacheManagementScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/HelpAndFeedbackScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/HomeScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/LearningReportScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/LoginScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/PaymentScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/ProfileScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/PurchaseHistoryScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/SettingsScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/ShareMaterialsScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/ShareRankingScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/ShareRevenueScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/screens/VideoPlayerScreen.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/viewmodel/LoginViewModel.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/viewmodel/PaymentViewModel.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/viewmodel/ProfileViewModel.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/viewmodel/PurchaseHistoryViewModel.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/viewmodel/SettingsViewModel.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/viewmodel/ShareRevenueViewModel.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/app/src/main/java/com/shuimu/videocourse/presentation/viewmodel/VideoPlayerViewModel.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ShuimuVideoCourseApp/build/reports/problems/problems-report.html" beforeDir="false" afterPath="$PROJECT_DIR$/ShuimuVideoCourseApp/build/reports/problems/problems-report.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <projects_view>
          <tree_state>
            <expand />
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yOajfUq4tGb1SLMHddbOxx52rb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "android.gradle.sync.needed": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "com.google.services.firebase.aqiPopupShown": "true",
    "git-widget-placeholder": "816d3fff",
    "last_opened_file_path": "D:/01-shuimu_01",
    "settings.editor.selected.configurable": "AndroidSdkUpdater"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-975db3bf15a3-31b6be0877a2-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a23e3fba-c9d5-4570-a94d-427fc5904d15" name="Changes" comment="" />
      <created>1749708209710</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749708209710</updated>
    </task>
    <servers />
  </component>
</project>