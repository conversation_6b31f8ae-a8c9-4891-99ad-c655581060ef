1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.shuimu.videocourse"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:7:5-81
13-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:8:5-80
14-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:8:22-77
15
16    <permission
16-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.shuimu.videocourse.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.shuimu.videocourse.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
21
22    <queries>
22-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:9:5-20:15
23        <intent> <!-- H5 支持手淘登录 -->
23-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:10:9-14:18
24            <action android:name="*" />
24-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:11:13-40
24-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:11:21-37
25
26            <data android:scheme="tbopen" />
26-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:13:13-45
26-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:13:19-42
27        </intent>
28
29        <package android:name="com.eg.android.AlipayGphone" /> <!-- 支付宝 app -->
29-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:16:9-63
29-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:16:18-60
30        <package android:name="com.eg.android.AlipayGphoneRC" /> <!-- 沙箱支付宝 app -->
30-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:17:9-65
30-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:17:18-62
31        <package android:name="hk.alipay.wallet" /> <!-- 香港版支付宝 app -->
31-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:18:9-52
31-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:18:18-49
32        <package android:name="hk.alipay.walletRC" /> <!-- 香港版支付宝 app -->
32-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:19:9-54
32-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:19:18-51
33    </queries>
34
35    <supports-screens
35-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:22:5-27:39
36        android:anyDensity="true"
36-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:23:9-34
37        android:largeScreens="true"
37-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:24:9-36
38        android:normalScreens="true"
38-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:25:9-37
39        android:resizeable="true"
39-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:26:9-34
40        android:smallScreens="true" />
40-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:27:9-36
41
42    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
42-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:31:5-76
42-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:31:22-73
43
44    <application
44-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:10:5-43:19
45        android:name="com.shuimu.videocourse.ShuimuVideoCourseApplication"
45-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:11:9-53
46        android:allowBackup="true"
46-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:12:9-35
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a8666d95f1b2e5574541723f67c731\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
48        android:dataExtractionRules="@xml/data_extraction_rules"
48-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:13:9-65
49        android:debuggable="true"
50        android:extractNativeLibs="false"
51        android:fullBackupContent="@xml/backup_rules"
51-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:14:9-54
52        android:icon="@android:drawable/sym_def_app_icon"
52-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:15:9-58
53        android:label="@string/app_name"
53-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:16:9-41
54        android:roundIcon="@android:drawable/sym_def_app_icon"
54-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:17:9-63
55        android:supportsRtl="true"
55-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:18:9-35
56        android:testOnly="true"
57        android:theme="@style/Theme.ShuimuVideoCourseApp" >
57-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:19:9-58
58        <activity
58-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:21:9-30:20
59            android:name="com.shuimu.videocourse.presentation.MainActivity"
59-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:22:13-54
60            android:exported="true"
60-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:23:13-36
61            android:label="@string/app_name"
61-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:24:13-45
62            android:theme="@style/Theme.ShuimuVideoCourseApp" >
62-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:25:13-62
63            <intent-filter>
63-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:26:13-29:29
64                <action android:name="android.intent.action.MAIN" />
64-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:17-69
64-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:25-66
65
66                <category android:name="android.intent.category.LAUNCHER" />
66-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:17-77
66-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:27-74
67            </intent-filter>
68        </activity>
69
70        <!-- 徽章测试Activity -->
71        <activity
71-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:33:9-42:20
72            android:name="com.shuimu.videocourse.presentation.components.basic.BadgeTestActivity"
72-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:34:13-76
73            android:exported="true"
73-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:35:13-36
74            android:label="徽章测试"
74-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:36:13-33
75            android:theme="@style/Theme.ShuimuVideoCourseApp" >
75-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:37:13-62
76            <intent-filter>
76-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:26:13-29:29
77                <action android:name="android.intent.action.MAIN" />
77-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:17-69
77-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:25-66
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:17-77
79-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:27-74
80            </intent-filter>
81        </activity>
82        <activity
82-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
83            android:name="androidx.compose.ui.tooling.PreviewActivity"
83-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
84            android:exported="true" />
84-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d19afc81da53c516dbfdfee6a6ad0fd7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
85        <activity
85-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
86            android:name="androidx.activity.ComponentActivity"
86-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
87            android:exported="true" />
87-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07e9dc649c465ca3a84099bfa64c118\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
88
89        <provider
89-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
90            android:name="androidx.startup.InitializationProvider"
90-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
91            android:authorities="com.shuimu.videocourse.androidx-startup"
91-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
92            android:exported="false" >
92-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
93            <meta-data
93-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
94                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
94-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
95                android:value="androidx.startup" />
95-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f52bb2417aa399586019b3d8deae497\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
96            <meta-data
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
97                android:name="androidx.emoji2.text.EmojiCompatInitializer"
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
98                android:value="androidx.startup" />
98-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c03c63743fcdf73da023f8df938905a3\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
99            <meta-data
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
100                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
101                android:value="androidx.startup" />
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
102        </provider>
103
104        <service
104-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
105            android:name="androidx.room.MultiInstanceInvalidationService"
105-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
106            android:directBootAware="true"
106-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
107            android:exported="false" />
107-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83993104fa5c0f7628db2f26241f914f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
108
109        <receiver
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
110            android:name="androidx.profileinstaller.ProfileInstallReceiver"
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
111            android:directBootAware="false"
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
112            android:enabled="true"
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
113            android:exported="true"
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
114            android:permission="android.permission.DUMP" >
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
116                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
119                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
122                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
125                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fa5cb4bd712a7df3863174357294ec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
126            </intent-filter>
127        </receiver>
128
129        <uses-library
129-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:34:9-36:40
130            android:name="org.apache.http.legacy"
130-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:35:13-50
131            android:required="false" />
131-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:36:13-37
132
133        <activity
133-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:38:9-43:20
134            android:name="com.alipay.sdk.app.H5PayActivity"
134-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:39:13-60
135            android:configChanges="orientation|keyboardHidden|navigation|screenSize|locale|keyboard|screenLayout|density|fontScale|layoutDirection|smallestScreenSize"
135-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:40:13-167
136            android:exported="false"
136-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:41:13-37
137            android:theme="@android:style/Theme.NoTitleBar" >
137-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:42:13-60
138        </activity>
139        <activity
139-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:44:9-49:20
140            android:name="com.alipay.sdk.app.H5AuthActivity"
140-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:45:13-61
141            android:configChanges="orientation|keyboardHidden|navigation|screenSize|locale|keyboard|screenLayout|density|fontScale|layoutDirection|smallestScreenSize"
141-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:46:13-167
142            android:exported="false"
142-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:47:13-37
143            android:theme="@android:style/Theme.NoTitleBar" >
143-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:48:13-60
144        </activity>
145        <activity
145-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:50:9-56:20
146            android:name="com.alipay.sdk.app.PayResultActivity"
146-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:51:13-64
147            android:configChanges="orientation|keyboardHidden|navigation|screenSize|locale|keyboard|screenLayout|density|fontScale|layoutDirection|smallestScreenSize"
147-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:52:13-167
148            android:exported="true"
148-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:53:13-36
149            android:launchMode="singleInstance"
149-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:54:13-48
150            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
150-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:55:13-72
151        </activity>
152        <activity
152-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:57:9-63:20
153            android:name="com.alipay.sdk.app.AlipayResultActivity"
153-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:58:13-67
154            android:configChanges="orientation|keyboardHidden|navigation|screenSize|locale|keyboard|screenLayout|density|fontScale|layoutDirection|smallestScreenSize"
154-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:59:13-167
155            android:exported="true"
155-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:60:13-36
156            android:launchMode="singleTask"
156-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:61:13-44
157            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
157-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:62:13-72
158        </activity>
159        <activity
159-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:64:9-70:20
160            android:name="com.alipay.sdk.app.H5OpenAuthActivity"
160-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:65:13-65
161            android:configChanges="orientation|keyboardHidden|navigation|screenSize|locale|keyboard|screenLayout|density|fontScale|layoutDirection|smallestScreenSize"
161-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:66:13-167
162            android:exported="false"
162-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:67:13-37
163            android:screenOrientation="behind"
163-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:68:13-47
164            android:windowSoftInputMode="adjustResize|stateHidden" >
164-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:69:13-67
165        </activity>
166        <activity
166-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:71:9-76:20
167            android:name="com.alipay.sdk.app.APayEntranceActivity"
167-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:72:13-67
168            android:configChanges="orientation|keyboardHidden|navigation|screenSize|locale|keyboard|screenLayout|density|fontScale|layoutDirection|smallestScreenSize"
168-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:73:13-167
169            android:exported="false"
169-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:74:13-37
170            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
170-->[com.alipay.sdk:alipaysdk-android:15.8.16] C:\Users\<USER>\.gradle\caches\8.13\transforms\da42a8426b4d8e400f403806d600f7f3\transformed\alipaysdk-android-15.8.16\AndroidManifest.xml:75:13-72
171        </activity>
172    </application>
173
174</manifest>
