package com.shuimu.videocourse.presentation.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.videocourse.presentation.components.components_full.user.UserProfile
import com.shuimu.videocourse.presentation.components.components_full.user.UserStats
import com.shuimu.videocourse.presentation.components.components_full.basic.ShuimuCard
import com.shuimu.videocourse.presentation.components.components_full.status.LoadingIndicator
import com.shuimu.videocourse.presentation.viewmodel.ProfileViewModel
import com.shuimu.videocourse.presentation.theme.ShuimuVideoCourseTheme

/**
 * 我的页面 - 对应03-我的页面.html
 * 
 * 功能特性：
 * - 用户信息展示（头像、昵称、购买状态）
 * - 学习统计（已购课程数量、学习时长、完成进度）
 * - 分享数据（分享次数、转化人数、累计收益）
 * - 功能入口（购买记录、缓存管理、学习报告、设置等）
 */
@Composable
fun ProfileScreen(
    onNavigateToPurchaseHistory: () -> Unit = {},
    onNavigateToCacheManagement: () -> Unit = {},
    onNavigateToLearningReport: () -> Unit = {},
    onNavigateToShareRevenue: () -> Unit = {},
    onNavigateToSettings: () -> Unit = {},
    onNavigateToLogin: () -> Unit = {},
    viewModel: ProfileViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(Unit) {
        viewModel.loadUserProfile()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    LoadingIndicator(message = "加载用户信息中...")
                }
            }
            
            !uiState.isLoggedIn -> {
                // 未登录状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Text(
                            text = "请先登录",
                            style = MaterialTheme.typography.headlineMedium
                        )
                        Button(
                            onClick = onNavigateToLogin
                        ) {
                            Text("立即登录")
                        }
                    }
                }
            }
            
            else -> {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 用户信息头部
                    item {
                        UserProfile(
                            user = uiState.user!!,
                            onEditProfile = { viewModel.editProfile() }
                        )
                    }
                    
                    // 学习统计
                    item {
                        ShuimuCard {
                            UserStats(
                                purchasedCoursesCount = uiState.purchasedCoursesCount,
                                totalLearningTime = uiState.totalLearningTime,
                                completionRate = uiState.completionRate,
                                shareCount = uiState.shareCount,
                                conversionCount = uiState.conversionCount,
                                totalRevenue = uiState.totalRevenue
                            )
                        }
                    }
                    
                    // 功能入口列表
                    item {
                        ShuimuCard {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                Text(
                                    text = "我的功能",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                
                                ProfileMenuItem(
                                    title = "购买记录",
                                    subtitle = "查看历史购买记录",
                                    onClick = onNavigateToPurchaseHistory
                                )
                                
                                ProfileMenuItem(
                                    title = "缓存管理",
                                    subtitle = "管理本地视频缓存",
                                    onClick = onNavigateToCacheManagement
                                )
                                
                                ProfileMenuItem(
                                    title = "学习报告",
                                    subtitle = "详细学习数据分析",
                                    onClick = onNavigateToLearningReport
                                )
                                
                                ProfileMenuItem(
                                    title = "分享收益",
                                    subtitle = "查看分享收益详情",
                                    onClick = onNavigateToShareRevenue
                                )
                                
                                ProfileMenuItem(
                                    title = "设置",
                                    subtitle = "应用设置和偏好",
                                    onClick = onNavigateToSettings
                                )
                            }
                        }
                    }
                    
                    // 退出登录
                    item {
                        OutlinedButton(
                            onClick = { viewModel.logout() },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("退出登录")
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ProfileMenuItem(
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ProfileScreenPreview() {
    ShuimuVideoCourseTheme {
        ProfileScreen()
    }
}
